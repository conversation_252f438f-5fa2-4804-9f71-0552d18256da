{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///f:/final%20year%20project/whole%20project/frontend/src/components/ConnectionStatus.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { isDemoMode } from '@/utils/api';\n\ninterface ConnectionStatusProps {\n  className?: string;\n}\n\nexport default function ConnectionStatus({ className = '' }: ConnectionStatusProps) {\n  const [isBackendConnected, setIsBackendConnected] = useState<boolean | null>(null);\n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    const checkBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:8000/health', {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n        });\n        \n        if (response.ok) {\n          setIsBackendConnected(true);\n        } else {\n          setIsBackendConnected(false);\n        }\n      } catch (error) {\n        setIsBackendConnected(false);\n      } finally {\n        setIsChecking(false);\n      }\n    };\n\n    checkBackendConnection();\n    \n    // Check connection every 30 seconds\n    const interval = setInterval(checkBackendConnection, 30000);\n    \n    return () => clearInterval(interval);\n  }, []);\n\n  if (isChecking) {\n    return (\n      <div className={`flex items-center space-x-2 ${className}`}>\n        <div className=\"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"></div>\n        <span className=\"text-sm text-gray-600\">Checking connection...</span>\n      </div>\n    );\n  }\n\n  const demoMode = isDemoMode();\n\n  if (isBackendConnected) {\n    return (\n      <div className={`flex items-center space-x-2 ${className}`}>\n        <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n        <span className=\"text-sm text-green-700\">Backend Connected</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`flex items-center space-x-2 ${className}`}>\n      <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n      <span className=\"text-sm text-orange-700\">\n        {demoMode ? 'Demo Mode' : 'Backend Offline'}\n      </span>\n      {!demoMode && (\n        <button\n          onClick={() => window.location.reload()}\n          className=\"text-xs text-blue-600 hover:text-blue-800 underline ml-2\"\n        >\n          Retry\n        </button>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASe,SAAS,iBAAiB,KAAyC;QAAzC,EAAE,YAAY,EAAE,EAAyB,GAAzC;;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC7E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;qEAAyB;oBAC7B,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,gCAAgC;4BAC3D,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;wBACF;wBAEA,IAAI,SAAS,EAAE,EAAE;4BACf,sBAAsB;wBACxB,OAAO;4BACL,sBAAsB;wBACxB;oBACF,EAAE,OAAO,OAAO;wBACd,sBAAsB;oBACxB,SAAU;wBACR,cAAc;oBAChB;gBACF;;YAEA;YAEA,oCAAoC;YACpC,MAAM,WAAW,YAAY,wBAAwB;YAErD;8CAAO,IAAM,cAAc;;QAC7B;qCAAG,EAAE;IAEL,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAW,AAAC,+BAAwC,OAAV;;8BAC7C,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;IAG9C;IAEA,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;IAE1B,IAAI,oBAAoB;QACtB,qBACE,6LAAC;YAAI,WAAW,AAAC,+BAAwC,OAAV;;8BAC7C,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAyB;;;;;;;;;;;;IAG/C;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,+BAAwC,OAAV;;0BAC7C,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAK,WAAU;0BACb,WAAW,cAAc;;;;;;YAE3B,CAAC,0BACA,6LAAC;gBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gBACrC,WAAU;0BACX;;;;;;;;;;;;AAMT;GAtEwB;KAAA", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///f:/final%20year%20project/whole%20project/frontend/src/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { useState } from 'react';\n\ninterface ThemeToggleProps {\n  className?: string;\n  showLabel?: boolean;\n}\n\nexport default function ThemeToggle({ className = '', showLabel = false }: ThemeToggleProps) {\n  const { theme, toggleTheme } = useTheme();\n  const [isAnimating, setIsAnimating] = useState(false);\n\n  const handleToggle = () => {\n    setIsAnimating(true);\n    toggleTheme();\n    setTimeout(() => setIsAnimating(false), 300);\n  };\n\n  return (\n    <div className={`flex items-center space-x-2 ${className}`}>\n      {showLabel && (\n        <span className={`text-sm font-medium transition-colors duration-300 ${\n          theme === 'dark' ? 'text-gray-300' : 'text-gray-700'\n        }`}>\n          {theme === 'dark' ? 'Dark' : 'Light'}\n        </span>\n      )}\n      \n      <button\n        onClick={handleToggle}\n        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 ${\n          theme === 'dark'\n            ? 'bg-gradient-to-r from-purple-600 to-blue-600 focus:ring-purple-500'\n            : 'bg-gradient-to-r from-yellow-400 to-orange-500 focus:ring-yellow-500'\n        } ${isAnimating ? 'scale-110' : 'scale-100'} hover:scale-105 shadow-lg`}\n        role=\"switch\"\n        aria-checked={theme === 'dark'}\n        aria-label=\"Toggle theme\"\n      >\n        <span\n          className={`inline-block h-4 w-4 transform rounded-full transition-all duration-300 ${\n            theme === 'dark' \n              ? 'translate-x-6 bg-gray-100' \n              : 'translate-x-1 bg-white'\n          } shadow-lg`}\n        >\n          <span className={`absolute inset-0 flex items-center justify-center text-xs transition-opacity duration-300 ${\n            theme === 'dark' ? 'opacity-100' : 'opacity-0'\n          }`}>\n            🌙\n          </span>\n          <span className={`absolute inset-0 flex items-center justify-center text-xs transition-opacity duration-300 ${\n            theme === 'light' ? 'opacity-100' : 'opacity-0'\n          }`}>\n            ☀️\n          </span>\n        </span>\n      </button>\n      \n      {/* Animated background effect */}\n      <div className={`absolute inset-0 rounded-full transition-opacity duration-300 ${\n        isAnimating ? 'opacity-20' : 'opacity-0'\n      } ${\n        theme === 'dark' \n          ? 'bg-gradient-to-r from-purple-400 to-blue-400' \n          : 'bg-gradient-to-r from-yellow-300 to-orange-400'\n      } blur-xl -z-10`} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,YAAY,KAAuD;QAAvD,EAAE,YAAY,EAAE,EAAE,YAAY,KAAK,EAAoB,GAAvD;;IAClC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB,eAAe;QACf;QACA,WAAW,IAAM,eAAe,QAAQ;IAC1C;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,+BAAwC,OAAV;;YAC5C,2BACC,6LAAC;gBAAK,WAAW,AAAC,sDAEjB,OADC,UAAU,SAAS,kBAAkB;0BAEpC,UAAU,SAAS,SAAS;;;;;;0BAIjC,6LAAC;gBACC,SAAS;gBACT,WAAW,AAAC,2IAIR,OAHF,UAAU,SACN,uEACA,wEACL,KAA2C,OAAxC,cAAc,cAAc,aAAY;gBAC5C,MAAK;gBACL,gBAAc,UAAU;gBACxB,cAAW;0BAEX,cAAA,6LAAC;oBACC,WAAW,AAAC,2EAIX,OAHC,UAAU,SACN,8BACA,0BACL;;sCAED,6LAAC;4BAAK,WAAW,AAAC,6FAEjB,OADC,UAAU,SAAS,gBAAgB;sCACjC;;;;;;sCAGJ,6LAAC;4BAAK,WAAW,AAAC,6FAEjB,OADC,UAAU,UAAU,gBAAgB;sCAClC;;;;;;;;;;;;;;;;;0BAOR,6LAAC;gBAAI,WAAW,AAAC,iEAGf,OAFA,cAAc,eAAe,aAC9B,KAIA,OAHC,UAAU,SACN,iDACA,kDACL;;;;;;;;;;;;AAGP;GA7DwB;;QACS,mIAAA,CAAA,WAAQ;;;KADjB", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///f:/final%20year%20project/whole%20project/frontend/src/components/AnimatedCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { ReactNode, useState } from 'react';\n\ninterface AnimatedCardProps {\n  children: ReactNode;\n  className?: string;\n  hover?: boolean;\n  gradient?: boolean;\n  glow?: boolean;\n  delay?: number;\n}\n\nexport default function AnimatedCard({ \n  children, \n  className = '', \n  hover = true, \n  gradient = false,\n  glow = false,\n  delay = 0\n}: AnimatedCardProps) {\n  const { theme } = useTheme();\n  const [isHovered, setIsHovered] = useState(false);\n\n  const baseClasses = `\n    transform transition-all duration-500 ease-out\n    ${delay > 0 ? `animate-fade-in-up` : ''}\n    ${hover ? 'hover:scale-105 hover:-translate-y-2' : ''}\n    ${glow && isHovered ? 'shadow-2xl' : 'shadow-lg'}\n  `;\n\n  const themeClasses = theme === 'dark'\n    ? `bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 text-white\n       ${glow && isHovered ? 'shadow-purple-500/25' : ''}\n       ${gradient ? 'bg-gradient-to-br from-purple-900/50 to-blue-900/50' : ''}`\n    : `bg-white border border-gray-200 text-gray-900\n       ${glow && isHovered ? 'shadow-blue-500/25' : ''}\n       ${gradient ? 'bg-gradient-to-br from-blue-50 to-purple-50' : ''}`;\n\n  return (\n    <div\n      className={`${baseClasses} ${themeClasses} ${className} rounded-xl overflow-hidden`}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n      style={{ animationDelay: `${delay}ms` }}\n    >\n      {/* Animated background overlay */}\n      {gradient && (\n        <div className={`absolute inset-0 opacity-0 transition-opacity duration-500 ${\n          isHovered ? 'opacity-100' : 'opacity-0'\n        } ${\n          theme === 'dark'\n            ? 'bg-gradient-to-br from-purple-600/20 to-blue-600/20'\n            : 'bg-gradient-to-br from-blue-400/10 to-purple-400/10'\n        }`} />\n      )}\n      \n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n      \n      {/* Glow effect */}\n      {glow && (\n        <div className={`absolute inset-0 rounded-xl transition-opacity duration-500 ${\n          isHovered ? 'opacity-100' : 'opacity-0'\n        } ${\n          theme === 'dark'\n            ? 'bg-gradient-to-r from-purple-600/10 to-blue-600/10'\n            : 'bg-gradient-to-r from-blue-400/10 to-purple-400/10'\n        } blur-xl -z-10`} />\n      )}\n    </div>\n  );\n}\n\n// Fade in animation component\nexport function FadeInUp({ children, delay = 0, className = '' }: {\n  children: ReactNode;\n  delay?: number;\n  className?: string;\n}) {\n  return (\n    <div \n      className={`animate-fade-in-up ${className}`}\n      style={{ animationDelay: `${delay}ms` }}\n    >\n      {children}\n    </div>\n  );\n}\n\n// Pulse animation component\nexport function PulseGlow({ children, color = 'blue' }: {\n  children: ReactNode;\n  color?: 'blue' | 'purple' | 'green' | 'red' | 'yellow';\n}) {\n  const { theme } = useTheme();\n  \n  const colorClasses = {\n    blue: theme === 'dark' ? 'shadow-blue-500/50' : 'shadow-blue-400/50',\n    purple: theme === 'dark' ? 'shadow-purple-500/50' : 'shadow-purple-400/50',\n    green: theme === 'dark' ? 'shadow-green-500/50' : 'shadow-green-400/50',\n    red: theme === 'dark' ? 'shadow-red-500/50' : 'shadow-red-400/50',\n    yellow: theme === 'dark' ? 'shadow-yellow-500/50' : 'shadow-yellow-400/50',\n  };\n\n  return (\n    <div className={`animate-pulse-glow ${colorClasses[color]}`}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;AAHA;;;AAce,SAAS,aAAa,KAOjB;QAPiB,EACnC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,WAAW,KAAK,EAChB,OAAO,KAAK,EACZ,QAAQ,CAAC,EACS,GAPiB;;IAQnC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,cAAc,AAAC,6DAGjB,OADA,QAAQ,IAAK,uBAAsB,IAAG,UAEtC,OADA,QAAQ,yCAAyC,IAAG,UACL,OAA/C,QAAQ,YAAY,eAAe,aAAY;IAGnD,MAAM,eAAe,UAAU,SAC3B,AAAC,yFAEE,OADA,QAAQ,YAAY,yBAAyB,IAAG,aACsB,OAAtE,WAAW,wDAAwD,MACtE,AAAC,yDAEE,OADA,QAAQ,YAAY,uBAAuB,IAAG,aACgB,OAA9D,WAAW,gDAAgD;IAElE,qBACE,6LAAC;QACC,WAAW,AAAC,GAAiB,OAAf,aAAY,KAAmB,OAAhB,cAAa,KAAa,OAAV,WAAU;QACvD,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;QACjC,OAAO;YAAE,gBAAgB,AAAC,GAAQ,OAAN,OAAM;QAAI;;YAGrC,0BACC,6LAAC;gBAAI,WAAW,AAAC,8DAGf,OAFA,YAAY,gBAAgB,aAC7B,KAIA,OAHC,UAAU,SACN,wDACA;;;;;;0BAKR,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIF,sBACC,6LAAC;gBAAI,WAAW,AAAC,+DAGf,OAFA,YAAY,gBAAgB,aAC7B,KAIA,OAHC,UAAU,SACN,uDACA,sDACL;;;;;;;;;;;;AAIT;GA7DwB;;QAQJ,mIAAA,CAAA,WAAQ;;;KARJ;AAgEjB,SAAS,SAAS,KAIxB;QAJwB,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,YAAY,EAAE,EAI7D,GAJwB;IAKvB,qBACE,6LAAC;QACC,WAAW,AAAC,sBAA+B,OAAV;QACjC,OAAO;YAAE,gBAAgB,AAAC,GAAQ,OAAN,OAAM;QAAI;kBAErC;;;;;;AAGP;MAbgB;AAgBT,SAAS,UAAU,KAGzB;QAHyB,EAAE,QAAQ,EAAE,QAAQ,MAAM,EAGnD,GAHyB;;IAIxB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,eAAe;QACnB,MAAM,UAAU,SAAS,uBAAuB;QAChD,QAAQ,UAAU,SAAS,yBAAyB;QACpD,OAAO,UAAU,SAAS,wBAAwB;QAClD,KAAK,UAAU,SAAS,sBAAsB;QAC9C,QAAQ,UAAU,SAAS,yBAAyB;IACtD;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,sBAAyC,OAApB,YAAY,CAAC,MAAM;kBACtD;;;;;;AAGP;IAnBgB;;QAII,mIAAA,CAAA,WAAQ;;;MAJZ", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///f:/final%20year%20project/whole%20project/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { reportsAPI, activitiesAPI, challengesAPI, emotionAPI, mockData, isDemoMode } from '@/utils/api';\nimport type { DailySummary, Activity, Challenge } from '@/utils/api';\nimport ConnectionStatus from '@/components/ConnectionStatus';\nimport ThemeToggle from '@/components/ThemeToggle';\nimport AnimatedCard, { FadeInUp, PulseGlow } from '@/components/AnimatedCard';\n\nexport default function Dashboard() {\n  const { user, isAuthenticated, logout } = useAuth();\n  const { theme } = useTheme();\n  const router = useRouter();\n  const [dailySummary, setDailySummary] = useState<DailySummary | null>(null);\n  const [activities, setActivities] = useState<Activity[]>([]);\n  const [challenges, setChallenges] = useState<Challenge[]>([]);\n  const [weeklyTrend, setWeeklyTrend] = useState<DailySummary[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/auth');\n      return;\n    }\n\n    const fetchDashboardData = async () => {\n      try {\n        setIsLoading(true);\n\n        // Check if we're in demo mode\n        if (isDemoMode()) {\n          // Use mock data for demo\n          setDailySummary(mockData.dailySummary);\n          setActivities(mockData.activities.slice(0, 3));\n          setChallenges(mockData.challenges.slice(0, 2));\n          setWeeklyTrend(mockData.weeklyTrend);\n        } else {\n          // Fetch all dashboard data in parallel from API\n          const [summaryRes, activitiesRes, challengesRes, trendRes] = await Promise.all([\n            reportsAPI.getDailySummary(),\n            activitiesAPI.getSuggestions(),\n            challengesAPI.getActive(),\n            reportsAPI.getWeeklyTrend()\n          ]);\n\n          setDailySummary(summaryRes);\n          setActivities(activitiesRes.activities.slice(0, 3)); // Show top 3 activities\n          setChallenges(challengesRes.slice(0, 2)); // Show top 2 challenges\n          setWeeklyTrend(trendRes.weekly_trend);\n        }\n      } catch (error) {\n        console.error('Error fetching dashboard data:', error);\n        // Fallback to mock data if API fails\n        setDailySummary(mockData.dailySummary);\n        setActivities(mockData.activities.slice(0, 3));\n        setChallenges(mockData.challenges.slice(0, 2));\n        setWeeklyTrend(mockData.weeklyTrend);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n  }, [isAuthenticated, router]);\n\n  const handleLogout = () => {\n    logout();\n    router.push('/auth');\n  };\n\n  const getEmotionColor = (emotion: string) => {\n    const colors: Record<string, string> = {\n      positive: 'text-green-600 bg-green-100',\n      negative: 'text-red-600 bg-red-100',\n      neutral: 'text-gray-600 bg-gray-100',\n    };\n    return colors[emotion] || 'text-gray-600 bg-gray-100';\n  };\n\n  const getScoreColor = (score: number) => {\n    if (score >= 70) return 'text-green-600';\n    if (score >= 40) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className={`text-3xl font-bold transition-colors duration-300 ${\n                theme === 'dark'\n                  ? 'bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent'\n                  : 'bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent'\n              }`}>\n                🧠 AI Driven Emotion Analysis\n              </h1>\n              <p className={`transition-colors duration-300 ${\n                theme === 'dark' ? 'text-gray-300' : 'text-gray-600'\n              }`}>\n                Welcome back, {user?.name}! Your intelligent wellness dashboard\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <ConnectionStatus />\n              <ThemeToggle showLabel={false} />\n              <nav className=\"flex space-x-4\">\n                <button\n                  onClick={() => router.push('/reports')}\n                  className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n                >\n                  Reports\n                </button>\n                <button\n                  onClick={() => router.push('/challenges')}\n                  className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n                >\n                  Challenges\n                </button>\n                <button\n                  onClick={() => router.push('/profile')}\n                  className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n                >\n                  Profile\n                </button>\n              </nav>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => router.push('/emotion-input')}\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-1\"\n                >\n                  <span>📝</span>\n                  <span>Text</span>\n                </button>\n                <button\n                  onClick={() => router.push('/emotion-input')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-1\"\n                >\n                  <span>📷</span>\n                  <span>Image</span>\n                </button>\n              </div>\n              <button\n                onClick={handleLogout}\n                className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Today's Summary */}\n          <FadeInUp delay={0}>\n            <AnimatedCard hover={true} gradient={true} glow={true} className=\"mb-6\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className={`text-lg leading-6 font-medium mb-4 flex items-center space-x-2 ${\n                  theme === 'dark' ? 'text-white' : 'text-gray-900'\n                }`}>\n                  <span className=\"text-2xl\">📊</span>\n                  <span>Today's AI Emotion Analysis</span>\n                </h3>\n              {dailySummary ? (\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"text-center\">\n                    <div className={`text-3xl font-bold ${getScoreColor(dailySummary.emotion_score)}`}>\n                      {dailySummary.emotion_score}/100\n                    </div>\n                    <div className=\"text-sm text-gray-500\">Emotion Score</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getEmotionColor(dailySummary.dominant_emotion)}`}>\n                      {dailySummary.dominant_emotion}\n                    </div>\n                    <div className=\"text-sm text-gray-500 mt-1\">Dominant Emotion</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-gray-900\">\n                      {dailySummary.total_logs}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">Logs Today</div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"text-center text-gray-500\">\n                  No emotion data for today. Start by detecting your emotions!\n                </div>\n              )}\n              </div>\n            </AnimatedCard>\n          </FadeInUp>\n\n          {/* Quick Actions */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n            <button\n              onClick={() => router.push('/emotion-input')}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center\"\n            >\n              <div className=\"text-2xl mb-2\">🎯</div>\n              <div className=\"font-medium\">Detect Emotion</div>\n            </button>\n            <button\n              onClick={() => router.push('/diary')}\n              className=\"bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center\"\n            >\n              <div className=\"text-2xl mb-2\">📝</div>\n              <div className=\"font-medium\">Emotion Diary</div>\n            </button>\n            <button\n              onClick={() => router.push('/reports')}\n              className=\"bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center\"\n            >\n              <div className=\"text-2xl mb-2\">📊</div>\n              <div className=\"font-medium\">View Reports</div>\n            </button>\n            <button\n              onClick={() => router.push('/challenges')}\n              className=\"bg-orange-600 hover:bg-orange-700 text-white p-4 rounded-lg text-center\"\n            >\n              <div className=\"text-2xl mb-2\">🏆</div>\n              <div className=\"font-medium\">Challenges</div>\n            </button>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Activity Suggestions */}\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                  Suggested Activities\n                </h3>\n                {activities.length > 0 ? (\n                  <div className=\"space-y-3\">\n                    {activities.map((activity) => (\n                      <div key={activity.id} className=\"border-l-4 border-blue-400 pl-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {activity.activity_description}\n                        </div>\n                        <div className=\"text-xs text-gray-500 mt-1\">\n                          {activity.category} • {activity.duration_minutes} min • {activity.difficulty_level}\n                        </div>\n                        {activity.link && (\n                          <a\n                            href={activity.link}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-blue-600 hover:text-blue-800 text-xs\"\n                          >\n                            Learn more →\n                          </a>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-gray-500 text-sm\">\n                    No activity suggestions available. Try detecting your emotions first!\n                  </div>\n                )}\n                <div className=\"mt-4\">\n                  <button\n                    onClick={() => router.push('/activities')}\n                    className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                  >\n                    View all activities →\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Active Challenges */}\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                  Active Challenges\n                </h3>\n                {challenges.length > 0 ? (\n                  <div className=\"space-y-4\">\n                    {challenges.map((challenge) => (\n                      <div key={challenge.id} className=\"border rounded-lg p-3\">\n                        <div className=\"flex justify-between items-start mb-2\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {challenge.title}\n                          </div>\n                          <div className=\"text-xs text-gray-500\">\n                            {challenge.completed_days}/{challenge.target_days} days\n                          </div>\n                        </div>\n                        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                          <div\n                            className=\"bg-blue-600 h-2 rounded-full\"\n                            style={{ width: `${challenge.progress}%` }}\n                          ></div>\n                        </div>\n                        <div className=\"text-xs text-gray-500 mt-1\">\n                          {challenge.progress}% complete\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-gray-500 text-sm\">\n                    No active challenges. Start a new challenge to improve your well-being!\n                  </div>\n                )}\n                <div className=\"mt-4\">\n                  <button\n                    onClick={() => router.push('/challenges')}\n                    className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                  >\n                    View all challenges →\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Weekly Trend Preview */}\n          {weeklyTrend.length > 0 && (\n            <div className=\"bg-white overflow-hidden shadow rounded-lg mt-6\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                  Weekly Emotion Trend\n                </h3>\n                <div className=\"grid grid-cols-7 gap-2\">\n                  {weeklyTrend.map((day, index) => (\n                    <div key={index} className=\"text-center\">\n                      <div className=\"text-xs text-gray-500 mb-1\">\n                        {new Date(day.date).toLocaleDateString('en', { weekday: 'short' })}\n                      </div>\n                      <div className={`w-8 h-8 mx-auto rounded-full flex items-center justify-center text-xs font-medium ${getEmotionColor(day.dominant_emotion)}`}>\n                        {Math.round(day.emotion_score)}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n                <div className=\"mt-4\">\n                  <button\n                    onClick={() => router.push('/reports')}\n                    className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                  >\n                    View detailed reports →\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAVA;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM;0DAAqB;oBACzB,IAAI;wBACF,aAAa;wBAEb,8BAA8B;wBAC9B,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;4BAChB,yBAAyB;4BACzB,gBAAgB,sHAAA,CAAA,WAAQ,CAAC,YAAY;4BACrC,cAAc,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG;4BAC3C,cAAc,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG;4BAC3C,eAAe,sHAAA,CAAA,WAAQ,CAAC,WAAW;wBACrC,OAAO;4BACL,gDAAgD;4BAChD,MAAM,CAAC,YAAY,eAAe,eAAe,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAC7E,sHAAA,CAAA,aAAU,CAAC,eAAe;gCAC1B,sHAAA,CAAA,gBAAa,CAAC,cAAc;gCAC5B,sHAAA,CAAA,gBAAa,CAAC,SAAS;gCACvB,sHAAA,CAAA,aAAU,CAAC,cAAc;6BAC1B;4BAED,gBAAgB;4BAChB,cAAc,cAAc,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,wBAAwB;4BAC7E,cAAc,cAAc,KAAK,CAAC,GAAG,KAAK,wBAAwB;4BAClE,eAAe,SAAS,YAAY;wBACtC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,qCAAqC;wBACrC,gBAAgB,sHAAA,CAAA,WAAQ,CAAC,YAAY;wBACrC,cAAc,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG;wBAC3C,cAAc,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG;wBAC3C,eAAe,sHAAA,CAAA,WAAQ,CAAC,WAAW;oBACrC,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;8BAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAiC;YACrC,UAAU;YACV,UAAU;YACV,SAAS;QACX;QACA,OAAO,MAAM,CAAC,QAAQ,IAAI;IAC5B;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAW,AAAC,qDAIf,OAHC,UAAU,SACN,+EACA;kDACF;;;;;;kDAGJ,6LAAC;wCAAE,WAAW,AAAC,kCAEd,OADC,UAAU,SAAS,kBAAkB;;4CACnC;4CACa,iBAAA,2BAAA,KAAM,IAAI;4CAAC;;;;;;;;;;;;;0CAG9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yIAAA,CAAA,UAAgB;;;;;kDACjB,6LAAC,oIAAA,CAAA,UAAW;wCAAC,WAAW;;;;;;kDACxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;kDAIH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAGV,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,qIAAA,CAAA,WAAQ;4BAAC,OAAO;sCACf,cAAA,6LAAC,qIAAA,CAAA,UAAY;gCAAC,OAAO;gCAAM,UAAU;gCAAM,MAAM;gCAAM,WAAU;0CAC/D,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,AAAC,kEAEf,OADC,UAAU,SAAS,eAAe;;8DAElC,6LAAC;oDAAK,WAAU;8DAAW;;;;;;8DAC3B,6LAAC;8DAAK;;;;;;;;;;;;wCAET,6BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,sBAA+D,OAA1C,cAAc,aAAa,aAAa;;gEAC3E,aAAa,aAAa;gEAAC;;;;;;;sEAE9B,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,0DAAwG,OAA/C,gBAAgB,aAAa,gBAAgB;sEACpH,aAAa,gBAAgB;;;;;;sEAEhC,6LAAC;4DAAI,WAAU;sEAA6B;;;;;;;;;;;;8DAE9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,aAAa,UAAU;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;iEAI3C,6LAAC;4CAAI,WAAU;sDAA4B;;;;;;;;;;;;;;;;;;;;;;sCASjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;sDAAc;;;;;;;;;;;;8CAE/B,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;sDAAc;;;;;;;;;;;;8CAE/B,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;sDAAc;;;;;;;;;;;;8CAE/B,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;sDAAc;;;;;;;;;;;;;;;;;;sCAIjC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;4CAGhE,WAAW,MAAM,GAAG,kBACnB,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAI,WAAU;0EACZ,SAAS,oBAAoB;;;;;;0EAEhC,6LAAC;gEAAI,WAAU;;oEACZ,SAAS,QAAQ;oEAAC;oEAAI,SAAS,gBAAgB;oEAAC;oEAAQ,SAAS,gBAAgB;;;;;;;4DAEnF,SAAS,IAAI,kBACZ,6LAAC;gEACC,MAAM,SAAS,IAAI;gEACnB,QAAO;gEACP,KAAI;gEACJ,WAAU;0EACX;;;;;;;uDAbK,SAAS,EAAE;;;;;;;;;qEAqBzB,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS,IAAM,OAAO,IAAI,CAAC;oDAC3B,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;8CAQP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;4CAGhE,WAAW,MAAM,GAAG,kBACnB,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,0BACf,6LAAC;wDAAuB,WAAU;;0EAChC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,UAAU,KAAK;;;;;;kFAElB,6LAAC;wEAAI,WAAU;;4EACZ,UAAU,cAAc;4EAAC;4EAAE,UAAU,WAAW;4EAAC;;;;;;;;;;;;;0EAGtD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,AAAC,GAAqB,OAAnB,UAAU,QAAQ,EAAC;oEAAG;;;;;;;;;;;0EAG7C,6LAAC;gEAAI,WAAU;;oEACZ,UAAU,QAAQ;oEAAC;;;;;;;;uDAhBd,UAAU,EAAE;;;;;;;;;qEAsB1B,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS,IAAM,OAAO,IAAI,CAAC;oDAC3B,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASR,YAAY,MAAM,GAAG,mBACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,KAAK,sBACrB,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEACZ,IAAI,KAAK,IAAI,IAAI,EAAE,kBAAkB,CAAC,MAAM;4DAAE,SAAS;wDAAQ;;;;;;kEAElE,6LAAC;wDAAI,WAAW,AAAC,qFAA0H,OAAtC,gBAAgB,IAAI,gBAAgB;kEACtI,KAAK,KAAK,CAAC,IAAI,aAAa;;;;;;;+CALvB;;;;;;;;;;kDAUd,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GApWwB;;QACoB,kIAAA,CAAA,UAAO;QAC/B,mIAAA,CAAA,WAAQ;QACX,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": ["file:///f:/final%20year%20project/whole%20project/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}