"""
Simple test script to verify backend setup
"""

def test_imports():
    """Test if all required modules can be imported"""
    try:
        print("Testing imports...")
        
        # Test FastAPI
        import fastapi
        print("✅ FastAPI imported successfully")
        
        # Test Uvicorn
        import uvicorn
        print("✅ Uvicorn imported successfully")
        
        # Test SQLAlchemy
        import sqlalchemy
        print("✅ SQLAlchemy imported successfully")
        
        # Test our app modules
        from app.utils.config import settings
        print("✅ App config imported successfully")
        
        from app.models.database import database
        print("✅ Database models imported successfully")
        
        print("\n🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_config():
    """Test configuration"""
    try:
        from app.utils.config import settings
        print(f"\n📊 Configuration test:")
        print(f"Database URL: {settings.database_url}")
        print(f"API Host: {settings.api_host}")
        print(f"API Port: {settings.api_port}")
        print(f"Debug Mode: {settings.debug}")
        print("✅ Configuration loaded successfully")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_app_creation():
    """Test FastAPI app creation"""
    try:
        from app.main import app
        print(f"\n🚀 FastAPI app created successfully")
        print(f"App title: {app.title}")
        print(f"App version: {app.version}")
        print("✅ App creation successful")
        return True
    except Exception as e:
        print(f"❌ App creation error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Emotion Detection System Backend Setup\n")
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_config),
        ("App Creation Test", test_app_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    print('='*50)
    
    if passed == total:
        print("🎉 All tests passed! Backend setup is working correctly.")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Setup database (PostgreSQL or SQLite)")
        print("3. Run: python start.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Make sure you're in the backend directory")
        print("2. Activate your virtual environment")
        print("3. Install dependencies: pip install -r requirements-minimal.txt")

if __name__ == "__main__":
    main()
