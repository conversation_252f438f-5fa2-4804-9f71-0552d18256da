{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { reportsAPI, activitiesAPI, challengesAPI, emotionAPI, mockData, isDemoMode } from '@/utils/api';\nimport type { DailySummary, Activity, Challenge } from '@/utils/api';\n\nexport default function Dashboard() {\n  const { user, isAuthenticated, logout } = useAuth();\n  const router = useRouter();\n  const [dailySummary, setDailySummary] = useState<DailySummary | null>(null);\n  const [activities, setActivities] = useState<Activity[]>([]);\n  const [challenges, setChallenges] = useState<Challenge[]>([]);\n  const [weeklyTrend, setWeeklyTrend] = useState<DailySummary[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/auth');\n      return;\n    }\n\n    const fetchDashboardData = async () => {\n      try {\n        setIsLoading(true);\n\n        // Check if we're in demo mode\n        if (isDemoMode()) {\n          // Use mock data for demo\n          setDailySummary(mockData.dailySummary);\n          setActivities(mockData.activities.slice(0, 3));\n          setChallenges(mockData.challenges.slice(0, 2));\n          setWeeklyTrend(mockData.weeklyTrend);\n        } else {\n          // Fetch all dashboard data in parallel from API\n          const [summaryRes, activitiesRes, challengesRes, trendRes] = await Promise.all([\n            reportsAPI.getDailySummary(),\n            activitiesAPI.getSuggestions(),\n            challengesAPI.getActive(),\n            reportsAPI.getWeeklyTrend()\n          ]);\n\n          setDailySummary(summaryRes);\n          setActivities(activitiesRes.activities.slice(0, 3)); // Show top 3 activities\n          setChallenges(challengesRes.slice(0, 2)); // Show top 2 challenges\n          setWeeklyTrend(trendRes.weekly_trend);\n        }\n      } catch (error) {\n        console.error('Error fetching dashboard data:', error);\n        // Fallback to mock data if API fails\n        setDailySummary(mockData.dailySummary);\n        setActivities(mockData.activities.slice(0, 3));\n        setChallenges(mockData.challenges.slice(0, 2));\n        setWeeklyTrend(mockData.weeklyTrend);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n  }, [isAuthenticated, router]);\n\n  const handleLogout = () => {\n    logout();\n    router.push('/auth');\n  };\n\n  const getEmotionColor = (emotion: string) => {\n    const colors: Record<string, string> = {\n      positive: 'text-green-600 bg-green-100',\n      negative: 'text-red-600 bg-red-100',\n      neutral: 'text-gray-600 bg-gray-100',\n    };\n    return colors[emotion] || 'text-gray-600 bg-gray-100';\n  };\n\n  const getScoreColor = (score: number) => {\n    if (score >= 70) return 'text-green-600';\n    if (score >= 40) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n              <p className=\"text-gray-600\">Welcome back, {user?.name}!</p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => router.push('/emotion-input')}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Detect Emotion\n              </button>\n              <button\n                onClick={handleLogout}\n                className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Today's Summary */}\n          <div className=\"bg-white overflow-hidden shadow rounded-lg mb-6\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                Today's Emotional Summary\n              </h3>\n              {dailySummary ? (\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"text-center\">\n                    <div className={`text-3xl font-bold ${getScoreColor(dailySummary.emotion_score)}`}>\n                      {dailySummary.emotion_score}/100\n                    </div>\n                    <div className=\"text-sm text-gray-500\">Emotion Score</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getEmotionColor(dailySummary.dominant_emotion)}`}>\n                      {dailySummary.dominant_emotion}\n                    </div>\n                    <div className=\"text-sm text-gray-500 mt-1\">Dominant Emotion</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-gray-900\">\n                      {dailySummary.total_logs}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">Logs Today</div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"text-center text-gray-500\">\n                  No emotion data for today. Start by detecting your emotions!\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n            <button\n              onClick={() => router.push('/emotion-input')}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center\"\n            >\n              <div className=\"text-2xl mb-2\">🎯</div>\n              <div className=\"font-medium\">Detect Emotion</div>\n            </button>\n            <button\n              onClick={() => router.push('/diary')}\n              className=\"bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center\"\n            >\n              <div className=\"text-2xl mb-2\">📝</div>\n              <div className=\"font-medium\">Emotion Diary</div>\n            </button>\n            <button\n              onClick={() => router.push('/reports')}\n              className=\"bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center\"\n            >\n              <div className=\"text-2xl mb-2\">📊</div>\n              <div className=\"font-medium\">View Reports</div>\n            </button>\n            <button\n              onClick={() => router.push('/challenges')}\n              className=\"bg-orange-600 hover:bg-orange-700 text-white p-4 rounded-lg text-center\"\n            >\n              <div className=\"text-2xl mb-2\">🏆</div>\n              <div className=\"font-medium\">Challenges</div>\n            </button>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Activity Suggestions */}\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                  Suggested Activities\n                </h3>\n                {activities.length > 0 ? (\n                  <div className=\"space-y-3\">\n                    {activities.map((activity) => (\n                      <div key={activity.id} className=\"border-l-4 border-blue-400 pl-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {activity.activity_description}\n                        </div>\n                        <div className=\"text-xs text-gray-500 mt-1\">\n                          {activity.category} • {activity.duration_minutes} min • {activity.difficulty_level}\n                        </div>\n                        {activity.link && (\n                          <a\n                            href={activity.link}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-blue-600 hover:text-blue-800 text-xs\"\n                          >\n                            Learn more →\n                          </a>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-gray-500 text-sm\">\n                    No activity suggestions available. Try detecting your emotions first!\n                  </div>\n                )}\n                <div className=\"mt-4\">\n                  <button\n                    onClick={() => router.push('/activities')}\n                    className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                  >\n                    View all activities →\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Active Challenges */}\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                  Active Challenges\n                </h3>\n                {challenges.length > 0 ? (\n                  <div className=\"space-y-4\">\n                    {challenges.map((challenge) => (\n                      <div key={challenge.id} className=\"border rounded-lg p-3\">\n                        <div className=\"flex justify-between items-start mb-2\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {challenge.title}\n                          </div>\n                          <div className=\"text-xs text-gray-500\">\n                            {challenge.completed_days}/{challenge.target_days} days\n                          </div>\n                        </div>\n                        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                          <div\n                            className=\"bg-blue-600 h-2 rounded-full\"\n                            style={{ width: `${challenge.progress}%` }}\n                          ></div>\n                        </div>\n                        <div className=\"text-xs text-gray-500 mt-1\">\n                          {challenge.progress}% complete\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-gray-500 text-sm\">\n                    No active challenges. Start a new challenge to improve your well-being!\n                  </div>\n                )}\n                <div className=\"mt-4\">\n                  <button\n                    onClick={() => router.push('/challenges')}\n                    className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                  >\n                    View all challenges →\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Weekly Trend Preview */}\n          {weeklyTrend.length > 0 && (\n            <div className=\"bg-white overflow-hidden shadow rounded-lg mt-6\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                  Weekly Emotion Trend\n                </h3>\n                <div className=\"grid grid-cols-7 gap-2\">\n                  {weeklyTrend.map((day, index) => (\n                    <div key={index} className=\"text-center\">\n                      <div className=\"text-xs text-gray-500 mb-1\">\n                        {new Date(day.date).toLocaleDateString('en', { weekday: 'short' })}\n                      </div>\n                      <div className={`w-8 h-8 mx-auto rounded-full flex items-center justify-center text-xs font-medium ${getEmotionColor(day.dominant_emotion)}`}>\n                        {Math.round(day.emotion_score)}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n                <div className=\"mt-4\">\n                  <button\n                    onClick={() => router.push('/reports')}\n                    className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                  >\n                    View detailed reports →\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM;0DAAqB;oBACzB,IAAI;wBACF,aAAa;wBAEb,8BAA8B;wBAC9B,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;4BAChB,yBAAyB;4BACzB,gBAAgB,sHAAA,CAAA,WAAQ,CAAC,YAAY;4BACrC,cAAc,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG;4BAC3C,cAAc,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG;4BAC3C,eAAe,sHAAA,CAAA,WAAQ,CAAC,WAAW;wBACrC,OAAO;4BACL,gDAAgD;4BAChD,MAAM,CAAC,YAAY,eAAe,eAAe,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAC7E,sHAAA,CAAA,aAAU,CAAC,eAAe;gCAC1B,sHAAA,CAAA,gBAAa,CAAC,cAAc;gCAC5B,sHAAA,CAAA,gBAAa,CAAC,SAAS;gCACvB,sHAAA,CAAA,aAAU,CAAC,cAAc;6BAC1B;4BAED,gBAAgB;4BAChB,cAAc,cAAc,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,wBAAwB;4BAC7E,cAAc,cAAc,KAAK,CAAC,GAAG,KAAK,wBAAwB;4BAClE,eAAe,SAAS,YAAY;wBACtC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,qCAAqC;wBACrC,gBAAgB,sHAAA,CAAA,WAAQ,CAAC,YAAY;wBACrC,cAAc,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG;wBAC3C,cAAc,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG;wBAC3C,eAAe,sHAAA,CAAA,WAAQ,CAAC,WAAW;oBACrC,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;8BAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAiC;YACrC,UAAU;YACV,UAAU;YACV,SAAS;QACX;QACA,OAAO,MAAM,CAAC,QAAQ,IAAI;IAC5B;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;;4CAAgB;4CAAe,iBAAA,2BAAA,KAAM,IAAI;4CAAC;;;;;;;;;;;;;0CAEzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;oCAGhE,6BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,sBAA+D,OAA1C,cAAc,aAAa,aAAa;;4DAC3E,aAAa,aAAa;4DAAC;;;;;;;kEAE9B,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,0DAAwG,OAA/C,gBAAgB,aAAa,gBAAgB;kEACpH,aAAa,gBAAgB;;;;;;kEAEhC,6LAAC;wDAAI,WAAU;kEAA6B;;;;;;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,aAAa,UAAU;;;;;;kEAE1B,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;6DAI3C,6LAAC;wCAAI,WAAU;kDAA4B;;;;;;;;;;;;;;;;;sCAQjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;sDAAc;;;;;;;;;;;;8CAE/B,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;sDAAc;;;;;;;;;;;;8CAE/B,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;sDAAc;;;;;;;;;;;;8CAE/B,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;sDAAc;;;;;;;;;;;;;;;;;;sCAIjC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;4CAGhE,WAAW,MAAM,GAAG,kBACnB,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAI,WAAU;0EACZ,SAAS,oBAAoB;;;;;;0EAEhC,6LAAC;gEAAI,WAAU;;oEACZ,SAAS,QAAQ;oEAAC;oEAAI,SAAS,gBAAgB;oEAAC;oEAAQ,SAAS,gBAAgB;;;;;;;4DAEnF,SAAS,IAAI,kBACZ,6LAAC;gEACC,MAAM,SAAS,IAAI;gEACnB,QAAO;gEACP,KAAI;gEACJ,WAAU;0EACX;;;;;;;uDAbK,SAAS,EAAE;;;;;;;;;qEAqBzB,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS,IAAM,OAAO,IAAI,CAAC;oDAC3B,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;8CAQP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;4CAGhE,WAAW,MAAM,GAAG,kBACnB,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,0BACf,6LAAC;wDAAuB,WAAU;;0EAChC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,UAAU,KAAK;;;;;;kFAElB,6LAAC;wEAAI,WAAU;;4EACZ,UAAU,cAAc;4EAAC;4EAAE,UAAU,WAAW;4EAAC;;;;;;;;;;;;;0EAGtD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,AAAC,GAAqB,OAAnB,UAAU,QAAQ,EAAC;oEAAG;;;;;;;;;;;0EAG7C,6LAAC;gEAAI,WAAU;;oEACZ,UAAU,QAAQ;oEAAC;;;;;;;;uDAhBd,UAAU,EAAE;;;;;;;;;qEAsB1B,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS,IAAM,OAAO,IAAI,CAAC;oDAC3B,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASR,YAAY,MAAM,GAAG,mBACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,KAAK,sBACrB,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEACZ,IAAI,KAAK,IAAI,IAAI,EAAE,kBAAkB,CAAC,MAAM;4DAAE,SAAS;wDAAQ;;;;;;kEAElE,6LAAC;wDAAI,WAAW,AAAC,qFAA0H,OAAtC,gBAAgB,IAAI,gBAAgB;kEACtI,KAAK,KAAK,CAAC,IAAI,aAAa;;;;;;;+CALvB;;;;;;;;;;kDAUd,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GApTwB;;QACoB,kIAAA,CAAA,UAAO;QAClC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}