@echo off
echo ========================================
echo   Emotion Detection System Startup
echo ========================================
echo.

echo 🔧 Setting up project directories...
cd /d "f:\final year project\whole project"

echo.
echo 🚀 Starting Backend Server...
echo ----------------------------------------
start "Backend Server" cmd /k "cd backend && echo Installing Python dependencies... && pip install -r requirements.txt && echo Starting backend server... && python start.py"

echo.
echo ⏳ Waiting for backend to initialize...
timeout /t 5 /nobreak > nul

echo.
echo 🌐 Starting Frontend Server...
echo ----------------------------------------
start "Frontend Server" cmd /k "cd frontend && echo Installing Node dependencies... && npm install && echo Starting frontend server... && npm run dev"

echo.
echo ✅ Both servers are starting up!
echo.
echo 📍 Access Points:
echo   Frontend: http://localhost:3000
echo   Backend API: http://localhost:8000/docs
echo   Health Check: http://localhost:8000/health
echo.
echo 🔑 Demo Login Credentials:
echo   Email: <EMAIL>
echo   Password: demo123
echo.
echo 💡 Tip: Wait for both servers to fully start before accessing the application
echo.
pause
