"""
Emotion detection API endpoints for text and image inputs
"""

from fastapi import APIRouter, HTTPException, Depends, status, File, UploadFile, Form
from datetime import datetime
from typing import Dict, List, Optional, Union
import re
import base64

from app.models.database import database, emotion_logs_table
from app.models.schemas import EmotionInput, EmotionResult, EmotionResponse, ImageEmotionInput
from app.api.auth import get_current_user
from app.services.emotion_service import EmotionDetectionService
from app.utils.config import get_primary_label

router = APIRouter()
emotion_service = EmotionDetectionService()

def preprocess_text(text: str) -> str:
    """
    Preprocess text for emotion detection
    """
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove special characters but keep punctuation that might be emotionally relevant
    text = re.sub(r'[^\w\s.,!?;:\'"()-]', '', text)
    
    # Ensure minimum length
    if len(text.strip()) < 3:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Text must be at least 3 characters long"
        )
    
    return text

@router.post("/detect", response_model=EmotionResult)
async def detect_emotion(
    emotion_input: EmotionInput,
    current_user = Depends(get_current_user)
):
    """
    Detect emotions from text input
    """
    try:
        # Preprocess text
        processed_text = preprocess_text(emotion_input.text)
        
        # Detect emotions using ML service
        emotions_dict = await emotion_service.detect_emotions(processed_text)
        
        # Calculate primary label
        primary_label = get_primary_label(emotions_dict)
        
        # Calculate confidence score (highest emotion probability)
        confidence_score = max(emotions_dict.values()) if emotions_dict else 0.0
        
        # Store in database
        query = emotion_logs_table.insert().values(
            user_id=current_user.id,
            date=datetime.utcnow(),
            text=processed_text,
            detected_emotions=emotions_dict,
            primary_label=primary_label,
            confidence_score=confidence_score,
            is_manual=False
        )
        
        log_id = await database.execute(query)
        
        return EmotionResult(
            emotions=emotions_dict,
            primary_label=primary_label,
            confidence_score=confidence_score,
            text=processed_text,
            detected_at=datetime.utcnow()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error detecting emotions: {str(e)}"
        )

@router.post("/detect-image", response_model=EmotionResult)
async def detect_emotion_from_image(
    image: UploadFile = File(...),
    current_user = Depends(get_current_user)
):
    """
    Detect emotions from image input (facial emotion recognition)
    """
    try:
        # Validate file type
        if not image.content_type or not image.content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be an image"
            )

        # Read image data
        image_data = await image.read()

        # Detect emotions using ML service
        emotions_dict = await emotion_service.detect_emotions_from_image(image_data)

        # Get primary emotion
        primary_label = get_primary_label(emotions_dict)
        confidence = emotions_dict.get(primary_label, 0.0)

        # Store in database
        query = emotion_logs_table.insert().values(
            user_id=current_user.id,
            text=f"Image emotion detection - {image.filename}",
            primary_label=primary_label,
            confidence=confidence,
            detected_emotions=emotions_dict,
            input_type="image",
            created_at=datetime.utcnow()
        )

        log_id = await database.execute(query)

        return EmotionResult(
            id=log_id,
            text=f"Image: {image.filename}",
            primary_label=primary_label,
            confidence=confidence,
            detected_emotions=emotions_dict,
            timestamp=datetime.utcnow().isoformat(),
            user_id=current_user.id,
            input_type="image"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error detecting emotions from image: {str(e)}"
        )

@router.post("/detect-base64", response_model=EmotionResult)
async def detect_emotion_from_base64_image(
    image_input: ImageEmotionInput,
    current_user = Depends(get_current_user)
):
    """
    Detect emotions from base64 encoded image
    """
    try:
        # Decode base64 image
        try:
            # Remove data URL prefix if present
            image_data = image_input.image_data
            if image_data.startswith('data:image'):
                image_data = image_data.split(',')[1]

            image_bytes = base64.b64decode(image_data)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid base64 image data"
            )

        # Detect emotions using ML service
        emotions_dict = await emotion_service.detect_emotions_from_image(image_bytes)

        # Get primary emotion
        primary_label = get_primary_label(emotions_dict)
        confidence = emotions_dict.get(primary_label, 0.0)

        # Store in database
        query = emotion_logs_table.insert().values(
            user_id=current_user.id,
            text="Image emotion detection (base64)",
            primary_label=primary_label,
            confidence=confidence,
            detected_emotions=emotions_dict,
            input_type="image",
            created_at=datetime.utcnow()
        )

        log_id = await database.execute(query)

        return EmotionResult(
            id=log_id,
            text="Image emotion detection",
            primary_label=primary_label,
            confidence=confidence,
            detected_emotions=emotions_dict,
            timestamp=datetime.utcnow().isoformat(),
            user_id=current_user.id,
            input_type="image"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error detecting emotions from image: {str(e)}"
        )

@router.get("/history", response_model=List[EmotionResponse])
async def get_emotion_history(
    limit: int = 50,
    offset: int = 0,
    current_user = Depends(get_current_user)
):
    """
    Get user's emotion detection history
    """
    try:
        query = (
            emotion_logs_table.select()
            .where(emotion_logs_table.c.user_id == current_user.id)
            .order_by(emotion_logs_table.c.created_at.desc())
            .limit(limit)
            .offset(offset)
        )
        
        results = await database.fetch_all(query)
        
        return [EmotionResponse(**dict(result)) for result in results]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching emotion history: {str(e)}"
        )

@router.get("/recent", response_model=EmotionResponse)
async def get_recent_emotion(current_user = Depends(get_current_user)):
    """
    Get user's most recent emotion detection
    """
    try:
        query = (
            emotion_logs_table.select()
            .where(emotion_logs_table.c.user_id == current_user.id)
            .order_by(emotion_logs_table.c.created_at.desc())
            .limit(1)
        )
        
        result = await database.fetch_one(query)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No emotion logs found"
            )
        
        return EmotionResponse(**dict(result))
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching recent emotion: {str(e)}"
        )

@router.delete("/history/{log_id}")
async def delete_emotion_log(
    log_id: int,
    current_user = Depends(get_current_user)
):
    """
    Delete a specific emotion log
    """
    try:
        # Check if log exists and belongs to user
        query = emotion_logs_table.select().where(
            (emotion_logs_table.c.id == log_id) & 
            (emotion_logs_table.c.user_id == current_user.id)
        )
        
        result = await database.fetch_one(query)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Emotion log not found"
            )
        
        # Delete the log
        delete_query = emotion_logs_table.delete().where(
            (emotion_logs_table.c.id == log_id) & 
            (emotion_logs_table.c.user_id == current_user.id)
        )
        
        await database.execute(delete_query)
        
        return {"message": "Emotion log deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting emotion log: {str(e)}"
        )

@router.get("/stats")
async def get_emotion_stats(current_user = Depends(get_current_user)):
    """
    Get emotion statistics for the user
    """
    try:
        # Get total logs count
        total_query = (
            emotion_logs_table.select()
            .where(emotion_logs_table.c.user_id == current_user.id)
        )
        
        all_logs = await database.fetch_all(total_query)
        
        if not all_logs:
            return {
                "total_logs": 0,
                "primary_label_distribution": {},
                "average_confidence": 0.0,
                "most_frequent_emotion": None
            }
        
        # Calculate statistics
        total_logs = len(all_logs)
        primary_labels = [log.primary_label for log in all_logs if log.primary_label]
        confidence_scores = [log.confidence_score for log in all_logs if log.confidence_score]
        
        # Primary label distribution
        label_counts = {}
        for label in primary_labels:
            label_counts[label] = label_counts.get(label, 0) + 1
        
        label_distribution = {
            label: (count / len(primary_labels)) * 100 
            for label, count in label_counts.items()
        } if primary_labels else {}
        
        # Average confidence
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
        
        # Most frequent emotion (from detected_emotions)
        emotion_counts = {}
        for log in all_logs:
            if log.detected_emotions:
                for emotion, score in log.detected_emotions.items():
                    if score > 0.1:  # Only count emotions with significant probability
                        emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        most_frequent_emotion = max(emotion_counts, key=emotion_counts.get) if emotion_counts else None
        
        return {
            "total_logs": total_logs,
            "primary_label_distribution": label_distribution,
            "average_confidence": round(avg_confidence, 3),
            "most_frequent_emotion": most_frequent_emotion,
            "emotion_counts": emotion_counts
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error calculating emotion stats: {str(e)}"
        )
