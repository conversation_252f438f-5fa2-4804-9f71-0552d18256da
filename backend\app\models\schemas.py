"""
Pydantic schemas for request/response models
"""

from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Dict, List, Any
from datetime import datetime

# User schemas
class UserCreate(BaseModel):
    name: str = Field(..., min_length=2, max_length=100)
    email: EmailStr
    password: str = Field(..., min_length=6)
    age: Optional[int] = Field(None, ge=13, le=120)
    gender: Optional[str] = Field(None, max_length=20)

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    id: int
    name: str
    email: str
    age: Optional[int]
    gender: Optional[str]
    notification_settings: Dict[str, Any] = {}
    created_at: datetime
    is_active: bool

class UserUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=2, max_length=100)
    age: Optional[int] = Field(None, ge=13, le=120)
    gender: Optional[str] = Field(None, max_length=20)
    notification_settings: Optional[Dict[str, Any]] = None

# Authentication schemas
class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse

class TokenData(BaseModel):
    email: Optional[str] = None

# Emotion detection schemas
class EmotionInput(BaseModel):
    text: str = Field(..., min_length=1, max_length=5000)

class ImageEmotionInput(BaseModel):
    image_data: str = Field(..., description="Base64 encoded image data")

class EmotionResult(BaseModel):
    id: Optional[int] = None
    text: str
    primary_label: str
    confidence: float
    detected_emotions: Dict[str, float]
    timestamp: str
    user_id: int
    input_type: Optional[str] = "text"

class EmotionResponse(BaseModel):
    id: int
    emotions: Dict[str, float]
    primary_label: str
    confidence_score: float
    text: Optional[str]
    notes: Optional[str]
    is_manual: bool
    created_at: datetime

# Emotion diary schemas
class EmotionLogCreate(BaseModel):
    emotion: str
    notes: Optional[str] = Field(None, max_length=1000)
    intensity: Optional[int] = Field(None, ge=1, le=10)

class EmotionLogResponse(BaseModel):
    id: int
    user_id: int
    date: datetime
    text: Optional[str]
    detected_emotions: Optional[Dict[str, float]]
    primary_label: Optional[str]
    confidence_score: Optional[float]
    notes: Optional[str]
    is_manual: bool
    created_at: datetime

# Activity schemas
class ActivityResponse(BaseModel):
    id: int
    emotion_category: str
    activity_description: str
    link: Optional[str]
    category: Optional[str]
    duration_minutes: Optional[int]
    difficulty_level: str

class ActivitySuggestion(BaseModel):
    activities: List[ActivityResponse]
    based_on_emotion: str
    suggestion_reason: str

# Challenge schemas
class ChallengeCreate(BaseModel):
    title: str = Field(..., max_length=200)
    description: str
    target_days: int = Field(7, ge=1, le=30)
    challenge_type: str

class ChallengeResponse(BaseModel):
    id: int
    user_id: int
    title: str
    description: str
    start_date: datetime
    end_date: datetime
    status: str
    progress: int
    target_days: int
    completed_days: int
    challenge_type: str
    reward_points: int
    created_at: datetime
    completed_at: Optional[datetime]

class ChallengeUpdate(BaseModel):
    progress: Optional[int] = Field(None, ge=0, le=100)
    completed_days: Optional[int] = Field(None, ge=0)
    status: Optional[str] = None

# Report schemas
class DailySummary(BaseModel):
    date: str
    primary_emotions: Dict[str, int]
    emotion_score: float
    total_logs: int
    dominant_emotion: str

class MonthlyReport(BaseModel):
    month: str
    year: int
    daily_summaries: List[DailySummary]
    emotion_distribution: Dict[str, float]
    average_score: float
    insights: List[str]
    total_logs: int
    most_frequent_emotion: str
    emotional_stability: str

# Notification schemas
class NotificationSettings(BaseModel):
    mood_checkins: bool = True
    stress_alerts: bool = True
    challenge_reminders: bool = True
    frequency: str = "daily"  # daily, twice_daily, weekly
    quiet_hours_start: Optional[str] = "22:00"
    quiet_hours_end: Optional[str] = "08:00"

class NotificationCreate(BaseModel):
    title: str = Field(..., max_length=200)
    message: str = Field(..., max_length=500)
    notification_type: str

class NotificationResponse(BaseModel):
    id: int
    user_id: int
    notification_type: str
    title: str
    message: str
    sent_at: datetime
    is_read: bool

# Badge schemas
class BadgeResponse(BaseModel):
    id: int
    user_id: int
    badge_name: str
    badge_description: Optional[str]
    earned_at: datetime
    challenge_id: Optional[int]
