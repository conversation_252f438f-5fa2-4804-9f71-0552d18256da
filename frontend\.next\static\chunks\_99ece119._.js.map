{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/app/profile/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { authAPI, notificationsAPI, isDemoMode } from '@/utils/api';\n\ninterface NotificationSettings {\n  mood_checkins: boolean;\n  stress_alerts: boolean;\n  challenge_reminders: boolean;\n  frequency: 'daily' | 'weekly' | 'monthly';\n}\n\ninterface UserProfile {\n  id: number;\n  name: string;\n  email: string;\n  age?: number;\n  gender?: string;\n  notification_settings: NotificationSettings;\n  created_at: string;\n  is_active: boolean;\n}\n\nexport default function ProfilePage() {\n  const { user, isAuthenticated, logout } = useAuth();\n  const router = useRouter();\n  const [profile, setProfile] = useState<UserProfile | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isEditing, setIsEditing] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    age: '',\n    gender: '',\n  });\n  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({\n    mood_checkins: true,\n    stress_alerts: true,\n    challenge_reminders: true,\n    frequency: 'daily' as const,\n  });\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/auth');\n      return;\n    }\n\n    const fetchProfile = async () => {\n      try {\n        setIsLoading(true);\n        \n        if (isDemoMode()) {\n          // Use mock data\n          const mockProfile: UserProfile = {\n            id: 1,\n            name: user?.name || 'Demo User',\n            email: user?.email || '<EMAIL>',\n            age: user?.age || 25,\n            gender: user?.gender || 'other',\n            notification_settings: user?.notification_settings || {\n              mood_checkins: true,\n              stress_alerts: true,\n              challenge_reminders: true,\n              frequency: 'daily',\n            },\n            created_at: user?.created_at || new Date().toISOString(),\n            is_active: true,\n          };\n          setProfile(mockProfile);\n          setFormData({\n            name: mockProfile.name,\n            age: mockProfile.age?.toString() || '',\n            gender: mockProfile.gender || '',\n          });\n          setNotificationSettings(mockProfile.notification_settings);\n        } else {\n          // Fetch real data\n          const profileData = await authAPI.getCurrentUser();\n          setProfile(profileData);\n          setFormData({\n            name: profileData.name,\n            age: profileData.age?.toString() || '',\n            gender: profileData.gender || '',\n          });\n          setNotificationSettings(profileData.notification_settings);\n        }\n      } catch (error) {\n        console.error('Error fetching profile:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchProfile();\n  }, [isAuthenticated, router, user]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleNotificationChange = (setting: keyof NotificationSettings, value: boolean | string) => {\n    setNotificationSettings(prev => ({\n      ...prev,\n      [setting]: value\n    }));\n  };\n\n  const handleSaveProfile = async () => {\n    try {\n      setIsSaving(true);\n      \n      if (isDemoMode()) {\n        // Mock save\n        const updatedProfile = {\n          ...profile!,\n          name: formData.name,\n          age: formData.age ? parseInt(formData.age) : undefined,\n          gender: formData.gender,\n          notification_settings: notificationSettings,\n        };\n        setProfile(updatedProfile);\n        \n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(updatedProfile));\n      } else {\n        // Save to backend\n        await authAPI.updateProfile({\n          name: formData.name,\n          age: formData.age ? parseInt(formData.age) : undefined,\n          gender: formData.gender,\n        });\n        \n        await notificationsAPI.updateSettings(notificationSettings);\n      }\n      \n      setIsEditing(false);\n    } catch (error) {\n      console.error('Error saving profile:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleDeleteAccount = async () => {\n    if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {\n      try {\n        if (isDemoMode()) {\n          // Mock delete\n          logout();\n          router.push('/auth');\n        } else {\n          // Delete from backend\n          await authAPI.deleteAccount();\n          logout();\n          router.push('/auth');\n        }\n      } catch (error) {\n        console.error('Error deleting account:', error);\n      }\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!profile) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Profile not found</h2>\n          <button\n            onClick={() => router.push('/dashboard')}\n            className=\"mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md\"\n          >\n            Back to Dashboard\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Profile & Settings</h1>\n              <p className=\"text-gray-600\">Manage your account and preferences</p>\n            </div>\n            <button\n              onClick={() => router.push('/dashboard')}\n              className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n            >\n              ← Back to Dashboard\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Profile Information */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900\">Profile Information</h3>\n                <button\n                  onClick={() => setIsEditing(!isEditing)}\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  {isEditing ? 'Cancel' : 'Edit Profile'}\n                </button>\n              </div>\n\n              {isEditing ? (\n                <div className=\"space-y-4\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                      Full Name\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"age\" className=\"block text-sm font-medium text-gray-700\">\n                      Age\n                    </label>\n                    <input\n                      type=\"number\"\n                      id=\"age\"\n                      name=\"age\"\n                      value={formData.age}\n                      onChange={handleInputChange}\n                      min=\"13\"\n                      max=\"120\"\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"gender\" className=\"block text-sm font-medium text-gray-700\">\n                      Gender\n                    </label>\n                    <select\n                      id=\"gender\"\n                      name=\"gender\"\n                      value={formData.gender}\n                      onChange={handleInputChange}\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    >\n                      <option value=\"\">Prefer not to say</option>\n                      <option value=\"male\">Male</option>\n                      <option value=\"female\">Female</option>\n                      <option value=\"other\">Other</option>\n                    </select>\n                  </div>\n\n                  <div className=\"flex space-x-3\">\n                    <button\n                      onClick={handleSaveProfile}\n                      disabled={isSaving}\n                      className=\"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                    >\n                      {isSaving ? 'Saving...' : 'Save Changes'}\n                    </button>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm font-medium text-gray-500\">Name:</span>\n                    <span className=\"text-sm text-gray-900\">{profile.name}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm font-medium text-gray-500\">Email:</span>\n                    <span className=\"text-sm text-gray-900\">{profile.email}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm font-medium text-gray-500\">Age:</span>\n                    <span className=\"text-sm text-gray-900\">{profile.age || 'Not specified'}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm font-medium text-gray-500\">Gender:</span>\n                    <span className=\"text-sm text-gray-900 capitalize\">{profile.gender || 'Not specified'}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm font-medium text-gray-500\">Member since:</span>\n                    <span className=\"text-sm text-gray-900\">\n                      {new Date(profile.created_at).toLocaleDateString()}\n                    </span>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Notification Settings */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Notification Settings</h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-900\">Mood Check-ins</label>\n                    <p className=\"text-sm text-gray-500\">Receive reminders to log your emotions</p>\n                  </div>\n                  <input\n                    type=\"checkbox\"\n                    checked={notificationSettings.mood_checkins}\n                    onChange={(e) => handleNotificationChange('mood_checkins', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-900\">Stress Alerts</label>\n                    <p className=\"text-sm text-gray-500\">Get notified when stress patterns are detected</p>\n                  </div>\n                  <input\n                    type=\"checkbox\"\n                    checked={notificationSettings.stress_alerts}\n                    onChange={(e) => handleNotificationChange('stress_alerts', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-900\">Challenge Reminders</label>\n                    <p className=\"text-sm text-gray-500\">Reminders about active challenges</p>\n                  </div>\n                  <input\n                    type=\"checkbox\"\n                    checked={notificationSettings.challenge_reminders}\n                    onChange={(e) => handleNotificationChange('challenge_reminders', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-900 mb-2\">Notification Frequency</label>\n                  <select\n                    value={notificationSettings.frequency}\n                    onChange={(e) => handleNotificationChange('frequency', e.target.value)}\n                    className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  >\n                    <option value=\"daily\">Daily</option>\n                    <option value=\"weekly\">Weekly</option>\n                    <option value=\"monthly\">Monthly</option>\n                  </select>\n                </div>\n\n                <button\n                  onClick={handleSaveProfile}\n                  disabled={isSaving}\n                  className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  {isSaving ? 'Saving...' : 'Save Notification Settings'}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Account Actions */}\n          <div className=\"bg-white shadow rounded-lg\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Account Actions</h3>\n              \n              <div className=\"space-y-4\">\n                <button\n                  onClick={logout}\n                  className=\"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  Sign Out\n                </button>\n                \n                <button\n                  onClick={handleDeleteAccount}\n                  className=\"w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  Delete Account\n                </button>\n                \n                <p className=\"text-xs text-gray-500 text-center\">\n                  Deleting your account will permanently remove all your data and cannot be undone.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAyBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,KAAK;QACL,QAAQ;IACV;IACA,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QACrF,eAAe;QACf,eAAe;QACf,qBAAqB;QACrB,WAAW;IACb;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM;sDAAe;oBACnB,IAAI;wBACF,aAAa;wBAEb,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;gCAoBT;4BAnBP,gBAAgB;4BAChB,MAAM,cAA2B;gCAC/B,IAAI;gCACJ,MAAM,CAAA,iBAAA,2BAAA,KAAM,IAAI,KAAI;gCACpB,OAAO,CAAA,iBAAA,2BAAA,KAAM,KAAK,KAAI;gCACtB,KAAK,CAAA,iBAAA,2BAAA,KAAM,GAAG,KAAI;gCAClB,QAAQ,CAAA,iBAAA,2BAAA,KAAM,MAAM,KAAI;gCACxB,uBAAuB,CAAA,iBAAA,2BAAA,KAAM,qBAAqB,KAAI;oCACpD,eAAe;oCACf,eAAe;oCACf,qBAAqB;oCACrB,WAAW;gCACb;gCACA,YAAY,CAAA,iBAAA,2BAAA,KAAM,UAAU,KAAI,IAAI,OAAO,WAAW;gCACtD,WAAW;4BACb;4BACA,WAAW;4BACX,YAAY;gCACV,MAAM,YAAY,IAAI;gCACtB,KAAK,EAAA,mBAAA,YAAY,GAAG,cAAf,uCAAA,iBAAiB,QAAQ,OAAM;gCACpC,QAAQ,YAAY,MAAM,IAAI;4BAChC;4BACA,wBAAwB,YAAY,qBAAqB;wBAC3D,OAAO;gCAME;4BALP,kBAAkB;4BAClB,MAAM,cAAc,MAAM,sHAAA,CAAA,UAAO,CAAC,cAAc;4BAChD,WAAW;4BACX,YAAY;gCACV,MAAM,YAAY,IAAI;gCACtB,KAAK,EAAA,mBAAA,YAAY,GAAG,cAAf,uCAAA,iBAAiB,QAAQ,OAAM;gCACpC,QAAQ,YAAY,MAAM,IAAI;4BAChC;4BACA,wBAAwB,YAAY,qBAAqB;wBAC3D;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;gCAAG;QAAC;QAAiB;QAAQ;KAAK;IAElC,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,2BAA2B,CAAC,SAAqC;QACrE,wBAAwB,CAAA,OAAQ,CAAC;gBAC/B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;IACH;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,YAAY;YAEZ,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;gBAChB,YAAY;gBACZ,MAAM,iBAAiB;oBACrB,GAAG,OAAO;oBACV,MAAM,SAAS,IAAI;oBACnB,KAAK,SAAS,GAAG,GAAG,SAAS,SAAS,GAAG,IAAI;oBAC7C,QAAQ,SAAS,MAAM;oBACvB,uBAAuB;gBACzB;gBACA,WAAW;gBAEX,sBAAsB;gBACtB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;YAC9C,OAAO;gBACL,kBAAkB;gBAClB,MAAM,sHAAA,CAAA,UAAO,CAAC,aAAa,CAAC;oBAC1B,MAAM,SAAS,IAAI;oBACnB,KAAK,SAAS,GAAG,GAAG,SAAS,SAAS,GAAG,IAAI;oBAC7C,QAAQ,SAAS,MAAM;gBACzB;gBAEA,MAAM,sHAAA,CAAA,mBAAgB,CAAC,cAAc,CAAC;YACxC;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,OAAO,OAAO,CAAC,gFAAgF;YACjG,IAAI;gBACF,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;oBAChB,cAAc;oBACd;oBACA,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,sBAAsB;oBACtB,MAAM,sHAAA,CAAA,UAAO,CAAC,aAAa;oBAC3B;oBACA,OAAO,IAAI,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,6LAAC;gDACC,SAAS,IAAM,aAAa,CAAC;gDAC7B,WAAU;0DAET,YAAY,WAAW;;;;;;;;;;;;oCAI3B,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAO,WAAU;kEAA0C;;;;;;kEAG1E,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,WAAU;;;;;;;;;;;;0DAId,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAM,WAAU;kEAA0C;;;;;;kEAGzE,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,GAAG;wDACnB,UAAU;wDACV,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;;0DAId,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAS,WAAU;kEAA0C;;;;;;kEAG5E,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,MAAM;wDACtB,UAAU;wDACV,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAG;;;;;;0EACjB,6LAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,6LAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,6LAAC;gEAAO,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;0DAI1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;8DAET,WAAW,cAAc;;;;;;;;;;;;;;;;6DAKhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAU;kEAAyB,QAAQ,IAAI;;;;;;;;;;;;0DAEvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAU;kEAAyB,QAAQ,KAAK;;;;;;;;;;;;0DAExD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAU;kEAAyB,QAAQ,GAAG,IAAI;;;;;;;;;;;;0DAE1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAU;kEAAoC,QAAQ,MAAM,IAAI;;;;;;;;;;;;0DAExE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAU;kEACb,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAEjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,6LAAC;wDACC,MAAK;wDACL,SAAS,qBAAqB,aAAa;wDAC3C,UAAU,CAAC,IAAM,yBAAyB,iBAAiB,EAAE,MAAM,CAAC,OAAO;wDAC3E,WAAU;;;;;;;;;;;;0DAId,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,6LAAC;wDACC,MAAK;wDACL,SAAS,qBAAqB,aAAa;wDAC3C,UAAU,CAAC,IAAM,yBAAyB,iBAAiB,EAAE,MAAM,CAAC,OAAO;wDAC3E,WAAU;;;;;;;;;;;;0DAId,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,6LAAC;wDACC,MAAK;wDACL,SAAS,qBAAqB,mBAAmB;wDACjD,UAAU,CAAC,IAAM,yBAAyB,uBAAuB,EAAE,MAAM,CAAC,OAAO;wDACjF,WAAU;;;;;;;;;;;;0DAId,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,6LAAC;wDACC,OAAO,qBAAqB,SAAS;wDACrC,UAAU,CAAC,IAAM,yBAAyB,aAAa,EAAE,MAAM,CAAC,KAAK;wDACrE,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAQ;;;;;;0EACtB,6LAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,6LAAC;gEAAO,OAAM;0EAAU;;;;;;;;;;;;;;;;;;0DAI5B,6LAAC;gDACC,SAAS;gDACT,UAAU;gDACV,WAAU;0DAET,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;sCAOlC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAEjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAID,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAID,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjE;GAxYwB;;QACoB,kIAAA,CAAA,UAAO;QAClC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}