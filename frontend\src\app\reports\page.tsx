'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { reportsAPI, isDemoMode, mockData } from '@/utils/api';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface EmotionStats {
  total_logs: number;
  emotion_distribution: { [key: string]: number };
  weekly_average: number;
  most_common_emotion: string;
}

export default function ReportsPage() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [weeklyTrend, setWeeklyTrend] = useState<any[]>([]);
  const [monthlyData, setMonthlyData] = useState<any[]>([]);
  const [emotionStats, setEmotionStats] = useState<EmotionStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('week');

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth');
      return;
    }

    const fetchReportsData = async () => {
      try {
        setIsLoading(true);
        
        if (isDemoMode()) {
          // Use mock data
          setWeeklyTrend(mockData.weeklyTrend);
          setMonthlyData(generateMockMonthlyData());
          setEmotionStats(generateMockEmotionStats());
        } else {
          // Fetch real data
          const [weeklyRes, monthlyRes, statsRes] = await Promise.all([
            reportsAPI.getWeeklyTrend(),
            reportsAPI.getMonthlyReport(),
            reportsAPI.getEmotionStats()
          ]);
          
          setWeeklyTrend(weeklyRes.weekly_trend);
          setMonthlyData(monthlyRes.monthly_data);
          setEmotionStats(statsRes);
        }
      } catch (error) {
        console.error('Error fetching reports data:', error);
        // Fallback to mock data
        setWeeklyTrend(mockData.weeklyTrend);
        setMonthlyData(generateMockMonthlyData());
        setEmotionStats(generateMockEmotionStats());
      } finally {
        setIsLoading(false);
      }
    };

    fetchReportsData();
  }, [isAuthenticated, router]);

  const generateMockMonthlyData = () => {
    const data = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split('T')[0],
        emotion_score: Math.floor(Math.random() * 40) + 50,
        total_logs: Math.floor(Math.random() * 8) + 1,
        dominant_emotion: ['happy', 'neutral', 'sad', 'excited', 'calm'][Math.floor(Math.random() * 5)]
      });
    }
    return data;
  };

  const generateMockEmotionStats = (): EmotionStats => ({
    total_logs: 156,
    emotion_distribution: {
      happy: 45,
      neutral: 32,
      sad: 18,
      excited: 25,
      calm: 20,
      anxious: 16
    },
    weekly_average: 75.2,
    most_common_emotion: 'happy'
  });

  const getChartData = () => {
    const data = selectedPeriod === 'week' ? weeklyTrend : monthlyData;
    
    return {
      labels: data.map(item => {
        const date = new Date(item.date);
        return selectedPeriod === 'week' 
          ? date.toLocaleDateString('en-US', { weekday: 'short' })
          : date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }),
      datasets: [
        {
          label: 'Emotion Score',
          data: data.map(item => item.emotion_score),
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
        },
      ],
    };
  };

  const getEmotionDistributionData = () => {
    if (!emotionStats) return { labels: [], datasets: [] };

    const emotions = Object.keys(emotionStats.emotion_distribution);
    const counts = Object.values(emotionStats.emotion_distribution);
    
    return {
      labels: emotions.map(emotion => emotion.charAt(0).toUpperCase() + emotion.slice(1)),
      datasets: [
        {
          data: counts,
          backgroundColor: [
            '#10B981', // green
            '#3B82F6', // blue
            '#EF4444', // red
            '#F59E0B', // yellow
            '#8B5CF6', // purple
            '#EC4899', // pink
          ],
          borderWidth: 2,
          borderColor: '#ffffff',
        },
      ],
    };
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: `Emotion Trends - ${selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)}`,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: true,
        text: 'Emotion Distribution',
      },
    },
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
              <p className="text-gray-600">Insights into your emotional patterns</p>
            </div>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-gray-500 hover:text-gray-700 text-sm font-medium"
            >
              ← Back to Dashboard
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Stats Overview */}
          {emotionStats && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="text-2xl">📊</div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Total Logs</dt>
                        <dd className="text-lg font-medium text-gray-900">{emotionStats.total_logs}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="text-2xl">📈</div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Weekly Average</dt>
                        <dd className="text-lg font-medium text-gray-900">{emotionStats.weekly_average}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="text-2xl">😊</div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Most Common</dt>
                        <dd className="text-lg font-medium text-gray-900 capitalize">{emotionStats.most_common_emotion}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="text-2xl">🎯</div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Emotions Tracked</dt>
                        <dd className="text-lg font-medium text-gray-900">{Object.keys(emotionStats.emotion_distribution).length}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Period Selector */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">Emotion Trends</h3>
                <div className="flex space-x-2">
                  {(['week', 'month'] as const).map((period) => (
                    <button
                      key={period}
                      onClick={() => setSelectedPeriod(period)}
                      className={`px-3 py-1 rounded-md text-sm font-medium ${
                        selectedPeriod === period
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      {period.charAt(0).toUpperCase() + period.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
              <div className="h-96">
                <Line data={getChartData()} options={chartOptions} />
              </div>
            </div>
          </div>

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Emotion Distribution */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Emotion Distribution</h3>
                <div className="h-80">
                  <Doughnut data={getEmotionDistributionData()} options={doughnutOptions} />
                </div>
              </div>
            </div>

            {/* Daily Activity */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Daily Activity</h3>
                <div className="h-80">
                  <Bar 
                    data={{
                      labels: weeklyTrend.map(item => new Date(item.date).toLocaleDateString('en-US', { weekday: 'short' })),
                      datasets: [{
                        label: 'Logs per Day',
                        data: weeklyTrend.map(item => item.total_logs),
                        backgroundColor: 'rgba(59, 130, 246, 0.6)',
                        borderColor: 'rgb(59, 130, 246)',
                        borderWidth: 1,
                      }]
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top' as const,
                        },
                        title: {
                          display: true,
                          text: 'Emotion Logs per Day',
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
