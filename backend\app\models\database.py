"""
Database configuration and models for Emotion Detection System
"""

import databases
import sqlalchemy
from sqlalchemy import (
    Column, Integer, String, DateTime, Text, JSON, 
    <PERSON><PERSON>ey, <PERSON><PERSON>an, Float, create_engine
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from app.utils.config import settings

# Database setup
DATABASE_URL = settings.database_url
database = databases.Database(DATABASE_URL)

# Configure engine based on database type
if DATABASE_URL.startswith("sqlite"):
    engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
else:
    engine = create_engine(DATABASE_URL)

metadata = sqlalchemy.MetaData()

# Base class for models
Base = declarative_base()

# Users table
users_table = sqlalchemy.Table(
    "users",
    metadata,
    Column("id", Integer, primary_key=True, index=True),
    <PERSON>umn("name", String(100), nullable=False),
    Column("email", String(255), unique=True, index=True, nullable=False),
    Column("password_hash", String(255), nullable=False),
    Column("age", Integer, nullable=True),
    Column("gender", String(20), nullable=True),
    Column("notification_settings", JSON, default={}),
    Column("created_at", DateTime, default=datetime.utcnow),
    Column("updated_at", DateTime, default=datetime.utcnow, onupdate=datetime.utcnow),
    Column("is_active", Boolean, default=True),
)

# Emotion logs table
emotion_logs_table = sqlalchemy.Table(
    "emotion_logs",
    metadata,
    Column("id", Integer, primary_key=True, index=True),
    Column("user_id", Integer, ForeignKey("users.id"), nullable=False),
    Column("date", DateTime, default=datetime.utcnow, nullable=False),
    Column("text", Text, nullable=True),
    Column("detected_emotions", JSON, nullable=True),
    Column("primary_label", String(20), nullable=True),
    Column("confidence_score", Float, nullable=True),
    Column("notes", Text, nullable=True),
    Column("is_manual", Boolean, default=False),
    Column("created_at", DateTime, default=datetime.utcnow),
)

# Activities table
activities_table = sqlalchemy.Table(
    "activities",
    metadata,
    Column("id", Integer, primary_key=True, index=True),
    Column("emotion_category", String(50), nullable=False),
    Column("activity_description", Text, nullable=False),
    Column("link", String(500), nullable=True),
    Column("category", String(50), nullable=True),  # meditation, exercise, social, etc.
    Column("duration_minutes", Integer, nullable=True),
    Column("difficulty_level", String(20), default="easy"),  # easy, medium, hard
    Column("is_active", Boolean, default=True),
    Column("created_at", DateTime, default=datetime.utcnow),
)

# Challenges table
challenges_table = sqlalchemy.Table(
    "challenges",
    metadata,
    Column("id", Integer, primary_key=True, index=True),
    Column("user_id", Integer, ForeignKey("users.id"), nullable=False),
    Column("title", String(200), nullable=False),
    Column("description", Text, nullable=False),
    Column("start_date", DateTime, nullable=False),
    Column("end_date", DateTime, nullable=False),
    Column("status", String(20), default="active"),  # active, completed, failed
    Column("progress", Integer, default=0),  # 0-100
    Column("target_days", Integer, default=7),
    Column("completed_days", Integer, default=0),
    Column("challenge_type", String(50), nullable=False),  # daily_log, gratitude, etc.
    Column("reward_points", Integer, default=10),
    Column("created_at", DateTime, default=datetime.utcnow),
    Column("completed_at", DateTime, nullable=True),
)

# User badges table (for gamification)
user_badges_table = sqlalchemy.Table(
    "user_badges",
    metadata,
    Column("id", Integer, primary_key=True, index=True),
    Column("user_id", Integer, ForeignKey("users.id"), nullable=False),
    Column("badge_name", String(100), nullable=False),
    Column("badge_description", Text, nullable=True),
    Column("earned_at", DateTime, default=datetime.utcnow),
    Column("challenge_id", Integer, ForeignKey("challenges.id"), nullable=True),
)

# Notification logs table
notification_logs_table = sqlalchemy.Table(
    "notification_logs",
    metadata,
    Column("id", Integer, primary_key=True, index=True),
    Column("user_id", Integer, ForeignKey("users.id"), nullable=False),
    Column("notification_type", String(50), nullable=False),  # mood_checkin, stress_alert
    Column("title", String(200), nullable=False),
    Column("message", Text, nullable=False),
    Column("sent_at", DateTime, default=datetime.utcnow),
    Column("is_read", Boolean, default=False),
    Column("firebase_message_id", String(255), nullable=True),
)

# Create all tables
metadata.create_all(bind=engine)
