module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/utils/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * API utility functions for the Emotion Detection System
 */ __turbopack_context__.s({
    "activitiesAPI": ()=>activitiesAPI,
    "authAPI": ()=>authAPI,
    "challengesAPI": ()=>challengesAPI,
    "default": ()=>__TURBOPACK__default__export__,
    "diaryAPI": ()=>diaryAPI,
    "emotionAPI": ()=>emotionAPI,
    "notificationsAPI": ()=>notificationsAPI,
    "reportsAPI": ()=>reportsAPI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
;
// API base URL
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:8000") || 'http://localhost:8000';
// Create axios instance
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
// Request interceptor to add auth token
api.interceptors.request.use((config)=>{
    const token = localStorage.getItem('access_token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Response interceptor to handle auth errors
api.interceptors.response.use((response)=>response, (error)=>{
    if (error.response?.status === 401) {
        // Clear token and redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('user');
        window.location.href = '/auth';
    }
    return Promise.reject(error);
});
const authAPI = {
    register: async (userData)=>{
        const response = await api.post('/auth/register', userData);
        return response.data;
    },
    login: async (credentials)=>{
        const response = await api.post('/auth/login', credentials);
        return response.data;
    },
    getCurrentUser: async ()=>{
        const response = await api.get('/auth/me');
        return response.data;
    },
    refreshToken: async ()=>{
        const response = await api.post('/auth/refresh');
        return response.data;
    }
};
const emotionAPI = {
    detectEmotion: async (text)=>{
        const response = await api.post('/emotion/detect', {
            text
        });
        return response.data;
    },
    getHistory: async (limit = 50, offset = 0)=>{
        const response = await api.get(`/emotion/history?limit=${limit}&offset=${offset}`);
        return response.data;
    },
    getRecent: async ()=>{
        const response = await api.get('/emotion/recent');
        return response.data;
    },
    getStats: async ()=>{
        const response = await api.get('/emotion/stats');
        return response.data;
    },
    deleteLog: async (logId)=>{
        await api.delete(`/emotion/history/${logId}`);
    }
};
const diaryAPI = {
    createLog: async (data)=>{
        const response = await api.post('/diary/log', data);
        return response.data;
    },
    getEntries: async (params)=>{
        const queryParams = new URLSearchParams();
        if (params?.start_date) queryParams.append('start_date', params.start_date);
        if (params?.end_date) queryParams.append('end_date', params.end_date);
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.offset) queryParams.append('offset', params.offset.toString());
        const response = await api.get(`/diary/entries?${queryParams}`);
        return response.data;
    },
    getEntry: async (entryId)=>{
        const response = await api.get(`/diary/entries/${entryId}`);
        return response.data;
    },
    updateEntry: async (entryId, notes)=>{
        const response = await api.put(`/diary/entries/${entryId}`, {
            notes
        });
        return response.data;
    },
    deleteEntry: async (entryId)=>{
        await api.delete(`/diary/entries/${entryId}`);
    },
    getTodayEntries: async ()=>{
        const response = await api.get('/diary/today');
        return response.data;
    },
    getPrompts: async ()=>{
        const response = await api.get('/diary/prompts');
        return response.data;
    }
};
const activitiesAPI = {
    getSuggestions: async (emotion)=>{
        const params = emotion ? `?emotion=${emotion}` : '';
        const response = await api.get(`/activities/suggestions${params}`);
        return response.data;
    },
    getAll: async ()=>{
        const response = await api.get('/activities/all');
        return response.data;
    },
    getByCategory: async (category)=>{
        const response = await api.get(`/activities/by-category/${category}`);
        return response.data;
    },
    getPersonalized: async ()=>{
        const response = await api.get('/activities/personalized');
        return response.data;
    }
};
const challengesAPI = {
    create: async (data)=>{
        const response = await api.post('/challenges/create', data);
        return response.data;
    },
    getActive: async ()=>{
        const response = await api.get('/challenges/active');
        return response.data;
    },
    getAll: async ()=>{
        const response = await api.get('/challenges/all');
        return response.data;
    },
    update: async (challengeId, data)=>{
        const response = await api.put(`/challenges/${challengeId}`, data);
        return response.data;
    },
    delete: async (challengeId)=>{
        await api.delete(`/challenges/${challengeId}`);
    },
    getTemplates: async ()=>{
        const response = await api.get('/challenges/templates');
        return response.data;
    },
    acceptTemplate: async (templateIndex)=>{
        const response = await api.post('/challenges/accept-template', {
            template_index: templateIndex
        });
        return response.data;
    },
    getBadges: async ()=>{
        const response = await api.get('/challenges/badges');
        return response.data;
    }
};
const reportsAPI = {
    getDailySummary: async (date)=>{
        const params = date ? `?target_date=${date}` : '';
        const response = await api.get(`/reports/daily-summary${params}`);
        return response.data;
    },
    getWeeklyTrend: async ()=>{
        const response = await api.get('/reports/weekly-trend');
        return response.data;
    },
    getMonthlyReport: async (month, year)=>{
        const params = new URLSearchParams();
        if (month) params.append('month', month.toString());
        if (year) params.append('year', year.toString());
        const response = await api.get(`/reports/monthly-report?${params}`);
        return response.data;
    }
};
const notificationsAPI = {
    getSettings: async ()=>{
        const response = await api.get('/notifications/settings');
        return response.data;
    },
    updateSettings: async (settings)=>{
        const response = await api.put('/notifications/settings', settings);
        return response.data;
    },
    getHistory: async (limit = 50, offset = 0)=>{
        const response = await api.get(`/notifications/history?limit=${limit}&offset=${offset}`);
        return response.data;
    },
    markAsRead: async (notificationId)=>{
        await api.put(`/notifications/${notificationId}/read`);
    },
    getUnreadCount: async ()=>{
        const response = await api.get('/notifications/unread-count');
        return response.data;
    },
    clearAll: async ()=>{
        await api.delete('/notifications/clear-all');
    }
};
const __TURBOPACK__default__export__ = api;
}),
"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AuthProvider": ()=>AuthProvider,
    "useAuth": ()=>useAuth
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useAuth = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
const AuthProvider = ({ children })=>{
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const isAuthenticated = !!user;
    // Check for existing token on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initAuth = async ()=>{
            const token = localStorage.getItem('access_token');
            const savedUser = localStorage.getItem('user');
            if (token && savedUser) {
                try {
                    // Verify token is still valid by fetching current user
                    const currentUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authAPI"].getCurrentUser();
                    setUser(currentUser);
                    localStorage.setItem('user', JSON.stringify(currentUser));
                } catch (error) {
                    // Token is invalid, clear storage
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('user');
                    setUser(null);
                }
            }
            setIsLoading(false);
        };
        initAuth();
    }, []);
    const login = async (email, password)=>{
        try {
            setIsLoading(true);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authAPI"].login({
                email,
                password
            });
            // Store token and user data
            localStorage.setItem('access_token', response.access_token);
            localStorage.setItem('user', JSON.stringify(response.user));
            setUser(response.user);
        } catch (error) {
            throw new Error(error.response?.data?.detail || 'Login failed');
        } finally{
            setIsLoading(false);
        }
    };
    const register = async (userData)=>{
        try {
            setIsLoading(true);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authAPI"].register(userData);
            // Store token and user data
            localStorage.setItem('access_token', response.access_token);
            localStorage.setItem('user', JSON.stringify(response.user));
            setUser(response.user);
        } catch (error) {
            throw new Error(error.response?.data?.detail || 'Registration failed');
        } finally{
            setIsLoading(false);
        }
    };
    const logout = ()=>{
        localStorage.removeItem('access_token');
        localStorage.removeItem('user');
        setUser(null);
    };
    const refreshUser = async ()=>{
        try {
            const currentUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authAPI"].getCurrentUser();
            setUser(currentUser);
            localStorage.setItem('user', JSON.stringify(currentUser));
        } catch (error) {
            console.error('Failed to refresh user:', error);
            logout();
        }
    };
    const value = {
        user,
        isLoading,
        isAuthenticated,
        login,
        register,
        logout,
        refreshUser
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 135,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__977aa3f2._.js.map