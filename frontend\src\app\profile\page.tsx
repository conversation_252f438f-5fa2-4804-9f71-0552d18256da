'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { authAPI, notificationsAPI, isDemoMode } from '@/utils/api';

interface NotificationSettings {
  mood_checkins: boolean;
  stress_alerts: boolean;
  challenge_reminders: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
}

interface UserProfile {
  id: number;
  name: string;
  email: string;
  age?: number;
  gender?: string;
  notification_settings: NotificationSettings;
  created_at: string;
  is_active: boolean;
}

export default function ProfilePage() {
  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    age: '',
    gender: '',
  });
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    mood_checkins: true,
    stress_alerts: true,
    challenge_reminders: true,
    frequency: 'daily' as const,
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth');
      return;
    }

    const fetchProfile = async () => {
      try {
        setIsLoading(true);
        
        if (isDemoMode()) {
          // Use mock data
          const mockProfile: UserProfile = {
            id: 1,
            name: user?.name || 'Demo User',
            email: user?.email || '<EMAIL>',
            age: user?.age || 25,
            gender: user?.gender || 'other',
            notification_settings: user?.notification_settings || {
              mood_checkins: true,
              stress_alerts: true,
              challenge_reminders: true,
              frequency: 'daily',
            },
            created_at: user?.created_at || new Date().toISOString(),
            is_active: true,
          };
          setProfile(mockProfile);
          setFormData({
            name: mockProfile.name,
            age: mockProfile.age?.toString() || '',
            gender: mockProfile.gender || '',
          });
          setNotificationSettings(mockProfile.notification_settings);
        } else {
          // Fetch real data
          const profileData = await authAPI.getCurrentUser();
          setProfile(profileData);
          setFormData({
            name: profileData.name,
            age: profileData.age?.toString() || '',
            gender: profileData.gender || '',
          });
          setNotificationSettings(profileData.notification_settings);
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [isAuthenticated, router, user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNotificationChange = (setting: keyof NotificationSettings, value: boolean | string) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  const handleSaveProfile = async () => {
    try {
      setIsSaving(true);
      
      if (isDemoMode()) {
        // Mock save
        const updatedProfile = {
          ...profile!,
          name: formData.name,
          age: formData.age ? parseInt(formData.age) : undefined,
          gender: formData.gender,
          notification_settings: notificationSettings,
        };
        setProfile(updatedProfile);
        
        // Update localStorage
        localStorage.setItem('user', JSON.stringify(updatedProfile));
      } else {
        // Save to backend
        await authAPI.updateProfile({
          name: formData.name,
          age: formData.age ? parseInt(formData.age) : undefined,
          gender: formData.gender,
        });
        
        await notificationsAPI.updateSettings(notificationSettings);
      }
      
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      try {
        if (isDemoMode()) {
          // Mock delete
          logout();
          router.push('/auth');
        } else {
          // Delete from backend
          await authAPI.deleteAccount();
          logout();
          router.push('/auth');
        }
      } catch (error) {
        console.error('Error deleting account:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Profile not found</h2>
          <button
            onClick={() => router.push('/dashboard')}
            className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Profile & Settings</h1>
              <p className="text-gray-600">Manage your account and preferences</p>
            </div>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-gray-500 hover:text-gray-700 text-sm font-medium"
            >
              ← Back to Dashboard
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Profile Information */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">Profile Information</h3>
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  {isEditing ? 'Cancel' : 'Edit Profile'}
                </button>
              </div>

              {isEditing ? (
                <div className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label htmlFor="age" className="block text-sm font-medium text-gray-700">
                      Age
                    </label>
                    <input
                      type="number"
                      id="age"
                      name="age"
                      value={formData.age}
                      onChange={handleInputChange}
                      min="13"
                      max="120"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label htmlFor="gender" className="block text-sm font-medium text-gray-700">
                      Gender
                    </label>
                    <select
                      id="gender"
                      name="gender"
                      value={formData.gender}
                      onChange={handleInputChange}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Prefer not to say</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={handleSaveProfile}
                      disabled={isSaving}
                      className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      {isSaving ? 'Saving...' : 'Save Changes'}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">Name:</span>
                    <span className="text-sm text-gray-900">{profile.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">Email:</span>
                    <span className="text-sm text-gray-900">{profile.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">Age:</span>
                    <span className="text-sm text-gray-900">{profile.age || 'Not specified'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">Gender:</span>
                    <span className="text-sm text-gray-900 capitalize">{profile.gender || 'Not specified'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">Member since:</span>
                    <span className="text-sm text-gray-900">
                      {new Date(profile.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Notification Settings */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Notification Settings</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-900">Mood Check-ins</label>
                    <p className="text-sm text-gray-500">Receive reminders to log your emotions</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={notificationSettings.mood_checkins}
                    onChange={(e) => handleNotificationChange('mood_checkins', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-900">Stress Alerts</label>
                    <p className="text-sm text-gray-500">Get notified when stress patterns are detected</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={notificationSettings.stress_alerts}
                    onChange={(e) => handleNotificationChange('stress_alerts', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-900">Challenge Reminders</label>
                    <p className="text-sm text-gray-500">Reminders about active challenges</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={notificationSettings.challenge_reminders}
                    onChange={(e) => handleNotificationChange('challenge_reminders', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-2">Notification Frequency</label>
                  <select
                    value={notificationSettings.frequency}
                    onChange={(e) => handleNotificationChange('frequency', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>

                <button
                  onClick={handleSaveProfile}
                  disabled={isSaving}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  {isSaving ? 'Saving...' : 'Save Notification Settings'}
                </button>
              </div>
            </div>
          </div>

          {/* Account Actions */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Account Actions</h3>
              
              <div className="space-y-4">
                <button
                  onClick={logout}
                  className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Sign Out
                </button>
                
                <button
                  onClick={handleDeleteAccount}
                  className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Delete Account
                </button>
                
                <p className="text-xs text-gray-500 text-center">
                  Deleting your account will permanently remove all your data and cannot be undone.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
