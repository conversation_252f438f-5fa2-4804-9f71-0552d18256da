"""
Main FastAPI application for Emotion Detection System
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import uvicorn
from contextlib import asynccontextmanager

from app.api import auth, emotion, diary, reports, activities, challenges, notifications
from app.models.database import database, engine, metadata
from app.utils.config import settings

# Create database tables
metadata.create_all(bind=engine)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await database.connect()
    print("Database connected")
    yield
    # Shutdown
    await database.disconnect()
    print("Database disconnected")

# Initialize FastAPI app
app = FastAPI(
    title="Emotion Detection System API",
    description="API for emotion, age, and gender detection with tracking and analytics",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins.split(","),  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Include API routers
app.include_router(auth.router, prefix="/auth", tags=["Authentication"])
app.include_router(emotion.router, prefix="/emotion", tags=["Emotion Detection"])
app.include_router(diary.router, prefix="/diary", tags=["Emotion Diary"])
app.include_router(reports.router, prefix="/reports", tags=["Reports"])
app.include_router(activities.router, prefix="/activities", tags=["Activities"])
app.include_router(challenges.router, prefix="/challenges", tags=["Challenges"])
app.include_router(notifications.router, prefix="/notifications", tags=["Notifications"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Emotion Detection System API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        await database.fetch_one("SELECT 1")
        return {
            "status": "healthy",
            "database": "connected",
            "timestamp": "2025-07-20T14:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Database connection failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
