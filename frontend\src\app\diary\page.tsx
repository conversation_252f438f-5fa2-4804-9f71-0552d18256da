'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { diaryAPI } from '@/utils/api';
import type { EmotionLog } from '@/utils/api';

export default function DiaryPage() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [entries, setEntries] = useState<EmotionLog[]>([]);
  const [prompts, setPrompts] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newEntry, setNewEntry] = useState({
    emotion: '',
    notes: '',
    intensity: 5,
  });

  const emotions = [
    'joy', 'sadness', 'anger', 'fear', 'surprise', 'disgust', 'love',
    'gratitude', 'optimism', 'pride', 'relief', 'excitement', 'caring',
    'nervousness', 'disappointment', 'embarrassment', 'grief', 'remorse'
  ];

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth');
      return;
    }

    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [entriesRes, promptsRes] = await Promise.all([
          diaryAPI.getEntries({ limit: 20 }),
          diaryAPI.getPrompts()
        ]);
        setEntries(entriesRes);
        setPrompts(promptsRes.prompts);
      } catch (error) {
        console.error('Error fetching diary data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [isAuthenticated, router]);

  const handleAddEntry = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newEntry.emotion) return;

    try {
      const entry = await diaryAPI.createLog({
        emotion: newEntry.emotion,
        notes: newEntry.notes || undefined,
        intensity: newEntry.intensity,
      });

      setEntries([entry, ...entries]);
      setNewEntry({ emotion: '', notes: '', intensity: 5 });
      setShowAddForm(false);
    } catch (error) {
      console.error('Error adding diary entry:', error);
    }
  };

  const handleDeleteEntry = async (entryId: number) => {
    if (!confirm('Are you sure you want to delete this entry?')) return;

    try {
      await diaryAPI.deleteEntry(entryId);
      setEntries(entries.filter(entry => entry.id !== entryId));
    } catch (error) {
      console.error('Error deleting entry:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getEmotionEmoji = (emotion: string) => {
    const emojiMap: Record<string, string> = {
      joy: '😊', sadness: '😢', anger: '😠', fear: '😨', surprise: '😲',
      disgust: '🤢', love: '❤️', gratitude: '🙏', optimism: '🌟', pride: '💪',
      relief: '😌', excitement: '🎉', caring: '🤗', nervousness: '😰',
      disappointment: '😞', embarrassment: '😳', grief: '😭', remorse: '😔'
    };
    return emojiMap[emotion] || '😐';
  };

  const getRandomPrompt = () => {
    return prompts[Math.floor(Math.random() * prompts.length)];
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Emotion Diary</h1>
              <p className="text-gray-600">Track and reflect on your emotions</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowAddForm(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Add Entry
              </button>
              <button
                onClick={() => router.push('/dashboard')}
                className="text-gray-500 hover:text-gray-700 text-sm font-medium"
              >
                ← Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Add Entry Form */}
          {showAddForm && (
            <div className="bg-white shadow rounded-lg mb-6">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Entry</h3>
                <form onSubmit={handleAddEntry}>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        How are you feeling?
                      </label>
                      <select
                        value={newEntry.emotion}
                        onChange={(e) => setNewEntry({ ...newEntry, emotion: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        required
                      >
                        <option value="">Select an emotion</option>
                        {emotions.map((emotion) => (
                          <option key={emotion} value={emotion}>
                            {getEmotionEmoji(emotion)} {emotion.charAt(0).toUpperCase() + emotion.slice(1)}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Intensity (1-10)
                      </label>
                      <input
                        type="range"
                        min="1"
                        max="10"
                        value={newEntry.intensity}
                        onChange={(e) => setNewEntry({ ...newEntry, intensity: parseInt(e.target.value) })}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>Mild</span>
                        <span className="font-medium">{newEntry.intensity}</span>
                        <span>Intense</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Notes (Optional)
                      </label>
                      {prompts.length > 0 && (
                        <div className="mb-2 p-3 bg-blue-50 rounded-md">
                          <p className="text-sm text-blue-800">
                            💡 Reflection prompt: {getRandomPrompt()}
                          </p>
                        </div>
                      )}
                      <textarea
                        value={newEntry.notes}
                        onChange={(e) => setNewEntry({ ...newEntry, notes: e.target.value })}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="What triggered this emotion? How did it make you feel?"
                      />
                    </div>
                  </div>

                  <div className="flex space-x-3 mt-6">
                    <button
                      type="submit"
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      Save Entry
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowAddForm(false)}
                      className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Entries List */}
          <div className="space-y-4">
            {entries.length > 0 ? (
              entries.map((entry) => (
                <div key={entry.id} className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">
                          {entry.detected_emotions 
                            ? getEmotionEmoji(Object.keys(entry.detected_emotions)[0] || 'neutral')
                            : getEmotionEmoji(entry.primary_label || 'neutral')
                          }
                        </span>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 capitalize">
                            {entry.primary_label || 'Unknown'}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {formatDate(entry.created_at)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                          entry.is_manual ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                        }`}>
                          {entry.is_manual ? 'Manual' : 'Detected'}
                        </span>
                        <button
                          onClick={() => handleDeleteEntry(entry.id)}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Delete
                        </button>
                      </div>
                    </div>

                    {entry.text && (
                      <div className="mb-3">
                        <p className="text-sm text-gray-700 italic">"{entry.text}"</p>
                      </div>
                    )}

                    {entry.notes && (
                      <div className="mb-3">
                        <p className="text-sm text-gray-700">{entry.notes}</p>
                      </div>
                    )}

                    {entry.detected_emotions && (
                      <div className="mt-3">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Emotion Breakdown:</h4>
                        <div className="flex flex-wrap gap-2">
                          {Object.entries(entry.detected_emotions)
                            .sort(([, a], [, b]) => b - a)
                            .slice(0, 3)
                            .map(([emotion, score]) => (
                              <span
                                key={emotion}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                              >
                                {emotion}: {Math.round(score * 100)}%
                              </span>
                            ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-12 text-center">
                  <div className="text-6xl mb-4">📝</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No diary entries yet</h3>
                  <p className="text-gray-500 mb-4">
                    Start tracking your emotions by adding your first entry or detecting emotions from text.
                  </p>
                  <div className="space-x-3">
                    <button
                      onClick={() => setShowAddForm(true)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      Add Manual Entry
                    </button>
                    <button
                      onClick={() => router.push('/emotion-input')}
                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      Detect Emotion
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
