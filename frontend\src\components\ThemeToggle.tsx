'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { useState } from 'react';

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
}

export default function ThemeToggle({ className = '', showLabel = false }: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();
  const [isAnimating, setIsAnimating] = useState(false);

  const handleToggle = () => {
    setIsAnimating(true);
    toggleTheme();
    setTimeout(() => setIsAnimating(false), 300);
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {showLabel && (
        <span className={`text-sm font-medium transition-colors duration-300 ${
          theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
        }`}>
          {theme === 'dark' ? 'Dark' : 'Light'}
        </span>
      )}
      
      <button
        onClick={handleToggle}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
          theme === 'dark'
            ? 'bg-gradient-to-r from-purple-600 to-blue-600 focus:ring-purple-500'
            : 'bg-gradient-to-r from-yellow-400 to-orange-500 focus:ring-yellow-500'
        } ${isAnimating ? 'scale-110' : 'scale-100'} hover:scale-105 shadow-lg`}
        role="switch"
        aria-checked={theme === 'dark'}
        aria-label="Toggle theme"
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full transition-all duration-300 ${
            theme === 'dark' 
              ? 'translate-x-6 bg-gray-100' 
              : 'translate-x-1 bg-white'
          } shadow-lg`}
        >
          <span className={`absolute inset-0 flex items-center justify-center text-xs transition-opacity duration-300 ${
            theme === 'dark' ? 'opacity-100' : 'opacity-0'
          }`}>
            🌙
          </span>
          <span className={`absolute inset-0 flex items-center justify-center text-xs transition-opacity duration-300 ${
            theme === 'light' ? 'opacity-100' : 'opacity-0'
          }`}>
            ☀️
          </span>
        </span>
      </button>
      
      {/* Animated background effect */}
      <div className={`absolute inset-0 rounded-full transition-opacity duration-300 ${
        isAnimating ? 'opacity-20' : 'opacity-0'
      } ${
        theme === 'dark' 
          ? 'bg-gradient-to-r from-purple-400 to-blue-400' 
          : 'bg-gradient-to-r from-yellow-300 to-orange-400'
      } blur-xl -z-10`} />
    </div>
  );
}
