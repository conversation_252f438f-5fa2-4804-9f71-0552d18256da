{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/app/auth/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\n\nexport default function AuthPage() {\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    age: '',\n    gender: '',\n  });\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isMounted, setIsMounted] = useState(false);\n\n  const { login, register, isAuthenticated } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  useEffect(() => {\n    if (isMounted && isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router, isMounted]);\n\n  if (!isMounted) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n\n    try {\n      if (isLogin) {\n        await login(formData.email, formData.password);\n      } else {\n        const userData = {\n          name: formData.name,\n          email: formData.email,\n          password: formData.password,\n          age: formData.age ? parseInt(formData.age) : undefined,\n          gender: formData.gender || undefined,\n        };\n        await register(userData);\n      }\n      router.push('/dashboard');\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const toggleMode = () => {\n    setIsLogin(!isLogin);\n    setError('');\n    setFormData({\n      name: '',\n      email: '',\n      password: '',\n      age: '',\n      gender: '',\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            {isLogin ? 'Sign in to your account' : 'Create your account'}\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            {isLogin ? \"Don't have an account? \" : 'Already have an account? '}\n            <button\n              type=\"button\"\n              onClick={toggleMode}\n              className=\"font-medium text-blue-600 hover:text-blue-500\"\n            >\n              {isLogin ? 'Sign up' : 'Sign in'}\n            </button>\n          </p>\n        </div>\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            {!isLogin && (\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                  Full Name\n                </label>\n                <input\n                  id=\"name\"\n                  name=\"name\"\n                  type=\"text\"\n                  required={!isLogin}\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Enter your full name\"\n                />\n              </div>\n            )}\n            \n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email Address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                value={formData.email}\n                onChange={handleInputChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Enter your email address\"\n              />\n            </div>\n            \n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                required\n                value={formData.password}\n                onChange={handleInputChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Enter your password\"\n              />\n            </div>\n            \n            {!isLogin && (\n              <>\n                <div>\n                  <label htmlFor=\"age\" className=\"block text-sm font-medium text-gray-700\">\n                    Age (Optional)\n                  </label>\n                  <input\n                    id=\"age\"\n                    name=\"age\"\n                    type=\"number\"\n                    min=\"13\"\n                    max=\"120\"\n                    value={formData.age}\n                    onChange={handleInputChange}\n                    className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                    placeholder=\"Enter your age\"\n                  />\n                </div>\n                \n                <div>\n                  <label htmlFor=\"gender\" className=\"block text-sm font-medium text-gray-700\">\n                    Gender (Optional)\n                  </label>\n                  <select\n                    id=\"gender\"\n                    name=\"gender\"\n                    value={formData.gender}\n                    onChange={handleInputChange}\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  >\n                    <option value=\"\">Select gender</option>\n                    <option value=\"male\">Male</option>\n                    <option value=\"female\">Female</option>\n                    <option value=\"other\">Other</option>\n                    <option value=\"prefer_not_to_say\">Prefer not to say</option>\n                  </select>\n                </div>\n              </>\n            )}\n          </div>\n\n          {error && (\n            <div className=\"rounded-md bg-red-50 p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? (\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n              ) : (\n                isLogin ? 'Sign In' : 'Sign Up'\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,QAAQ;IACV;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,aAAa;QACf;6BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,aAAa,iBAAiB;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;6BAAG;QAAC;QAAiB;QAAQ;KAAU;IAEvC,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,aAAa;QAEb,IAAI;YACF,IAAI,SAAS;gBACX,MAAM,MAAM,SAAS,KAAK,EAAE,SAAS,QAAQ;YAC/C,OAAO;gBACL,MAAM,WAAW;oBACf,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,KAAK,SAAS,GAAG,GAAG,SAAS,SAAS,GAAG,IAAI;oBAC7C,QAAQ,SAAS,MAAM,IAAI;gBAC7B;gBACA,MAAM,SAAS;YACjB;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,WAAW,CAAC;QACZ,SAAS;QACT,YAAY;YACV,MAAM;YACN,OAAO;YACP,UAAU;YACV,KAAK;YACL,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCACX,UAAU,4BAA4B;;;;;;sCAEzC,6LAAC;4BAAE,WAAU;;gCACV,UAAU,4BAA4B;8CACvC,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CAET,UAAU,YAAY;;;;;;;;;;;;;;;;;;8BAK7B,6LAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,6LAAC;4BAAI,WAAU;;gCACZ,CAAC,yBACA,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA0C;;;;;;sDAG1E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,UAAU,CAAC;4CACX,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;gCAIf,CAAC,yBACA;;sDACE,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAM,WAAU;8DAA0C;;;;;;8DAGzE,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,SAAS,GAAG;oDACnB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAS,WAAU;8DAA0C;;;;;;8DAG5E,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,MAAM;oDACtB,UAAU;oDACV,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAG;;;;;;sEACjB,6LAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,6LAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;sEACtB,6LAAC;4DAAO,OAAM;sEAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;wBAO3C,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;sCAI3C,6LAAC;sCACC,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,0BACC,6LAAC;oCAAI,WAAU;;;;;2CAEf,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC;GAvNwB;;QAauB,kIAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAdF", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}