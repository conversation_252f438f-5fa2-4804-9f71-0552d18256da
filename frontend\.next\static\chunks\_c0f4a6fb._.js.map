{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/components/ConnectionStatus.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { isDemoMode } from '@/utils/api';\n\ninterface ConnectionStatusProps {\n  className?: string;\n}\n\nexport default function ConnectionStatus({ className = '' }: ConnectionStatusProps) {\n  const [isBackendConnected, setIsBackendConnected] = useState<boolean | null>(null);\n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    const checkBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:8000/health', {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n        });\n        \n        if (response.ok) {\n          setIsBackendConnected(true);\n        } else {\n          setIsBackendConnected(false);\n        }\n      } catch (error) {\n        setIsBackendConnected(false);\n      } finally {\n        setIsChecking(false);\n      }\n    };\n\n    checkBackendConnection();\n    \n    // Check connection every 30 seconds\n    const interval = setInterval(checkBackendConnection, 30000);\n    \n    return () => clearInterval(interval);\n  }, []);\n\n  if (isChecking) {\n    return (\n      <div className={`flex items-center space-x-2 ${className}`}>\n        <div className=\"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"></div>\n        <span className=\"text-sm text-gray-600\">Checking connection...</span>\n      </div>\n    );\n  }\n\n  const demoMode = isDemoMode();\n\n  if (isBackendConnected) {\n    return (\n      <div className={`flex items-center space-x-2 ${className}`}>\n        <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n        <span className=\"text-sm text-green-700\">Backend Connected</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`flex items-center space-x-2 ${className}`}>\n      <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n      <span className=\"text-sm text-orange-700\">\n        {demoMode ? 'Demo Mode' : 'Backend Offline'}\n      </span>\n      {!demoMode && (\n        <button\n          onClick={() => window.location.reload()}\n          className=\"text-xs text-blue-600 hover:text-blue-800 underline ml-2\"\n        >\n          Retry\n        </button>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASe,SAAS,iBAAiB,KAAyC;QAAzC,EAAE,YAAY,EAAE,EAAyB,GAAzC;;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC7E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;qEAAyB;oBAC7B,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,gCAAgC;4BAC3D,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;wBACF;wBAEA,IAAI,SAAS,EAAE,EAAE;4BACf,sBAAsB;wBACxB,OAAO;4BACL,sBAAsB;wBACxB;oBACF,EAAE,OAAO,OAAO;wBACd,sBAAsB;oBACxB,SAAU;wBACR,cAAc;oBAChB;gBACF;;YAEA;YAEA,oCAAoC;YACpC,MAAM,WAAW,YAAY,wBAAwB;YAErD;8CAAO,IAAM,cAAc;;QAC7B;qCAAG,EAAE;IAEL,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAW,AAAC,+BAAwC,OAAV;;8BAC7C,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;IAG9C;IAEA,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;IAE1B,IAAI,oBAAoB;QACtB,qBACE,6LAAC;YAAI,WAAW,AAAC,+BAAwC,OAAV;;8BAC7C,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAyB;;;;;;;;;;;;IAG/C;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,+BAAwC,OAAV;;0BAC7C,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAK,WAAU;0BACb,WAAW,cAAc;;;;;;YAE3B,CAAC,0BACA,6LAAC;gBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gBACrC,WAAU;0BACX;;;;;;;;;;;;AAMT;GAtEwB;KAAA", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { useState } from 'react';\n\ninterface ThemeToggleProps {\n  className?: string;\n  showLabel?: boolean;\n}\n\nexport default function ThemeToggle({ className = '', showLabel = false }: ThemeToggleProps) {\n  const { theme, toggleTheme } = useTheme();\n  const [isAnimating, setIsAnimating] = useState(false);\n\n  const handleToggle = () => {\n    setIsAnimating(true);\n    toggleTheme();\n    setTimeout(() => setIsAnimating(false), 300);\n  };\n\n  return (\n    <div className={`flex items-center space-x-2 ${className}`}>\n      {showLabel && (\n        <span className={`text-sm font-medium transition-colors duration-300 ${\n          theme === 'dark' ? 'text-gray-300' : 'text-gray-700'\n        }`}>\n          {theme === 'dark' ? 'Dark' : 'Light'}\n        </span>\n      )}\n      \n      <button\n        onClick={handleToggle}\n        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 ${\n          theme === 'dark'\n            ? 'bg-gradient-to-r from-purple-600 to-blue-600 focus:ring-purple-500'\n            : 'bg-gradient-to-r from-yellow-400 to-orange-500 focus:ring-yellow-500'\n        } ${isAnimating ? 'scale-110' : 'scale-100'} hover:scale-105 shadow-lg`}\n        role=\"switch\"\n        aria-checked={theme === 'dark'}\n        aria-label=\"Toggle theme\"\n      >\n        <span\n          className={`inline-block h-4 w-4 transform rounded-full transition-all duration-300 ${\n            theme === 'dark' \n              ? 'translate-x-6 bg-gray-100' \n              : 'translate-x-1 bg-white'\n          } shadow-lg`}\n        >\n          <span className={`absolute inset-0 flex items-center justify-center text-xs transition-opacity duration-300 ${\n            theme === 'dark' ? 'opacity-100' : 'opacity-0'\n          }`}>\n            🌙\n          </span>\n          <span className={`absolute inset-0 flex items-center justify-center text-xs transition-opacity duration-300 ${\n            theme === 'light' ? 'opacity-100' : 'opacity-0'\n          }`}>\n            ☀️\n          </span>\n        </span>\n      </button>\n      \n      {/* Animated background effect */}\n      <div className={`absolute inset-0 rounded-full transition-opacity duration-300 ${\n        isAnimating ? 'opacity-20' : 'opacity-0'\n      } ${\n        theme === 'dark' \n          ? 'bg-gradient-to-r from-purple-400 to-blue-400' \n          : 'bg-gradient-to-r from-yellow-300 to-orange-400'\n      } blur-xl -z-10`} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,YAAY,KAAuD;QAAvD,EAAE,YAAY,EAAE,EAAE,YAAY,KAAK,EAAoB,GAAvD;;IAClC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB,eAAe;QACf;QACA,WAAW,IAAM,eAAe,QAAQ;IAC1C;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,+BAAwC,OAAV;;YAC5C,2BACC,6LAAC;gBAAK,WAAW,AAAC,sDAEjB,OADC,UAAU,SAAS,kBAAkB;0BAEpC,UAAU,SAAS,SAAS;;;;;;0BAIjC,6LAAC;gBACC,SAAS;gBACT,WAAW,AAAC,2IAIR,OAHF,UAAU,SACN,uEACA,wEACL,KAA2C,OAAxC,cAAc,cAAc,aAAY;gBAC5C,MAAK;gBACL,gBAAc,UAAU;gBACxB,cAAW;0BAEX,cAAA,6LAAC;oBACC,WAAW,AAAC,2EAIX,OAHC,UAAU,SACN,8BACA,0BACL;;sCAED,6LAAC;4BAAK,WAAW,AAAC,6FAEjB,OADC,UAAU,SAAS,gBAAgB;sCACjC;;;;;;sCAGJ,6LAAC;4BAAK,WAAW,AAAC,6FAEjB,OADC,UAAU,UAAU,gBAAgB;sCAClC;;;;;;;;;;;;;;;;;0BAOR,6LAAC;gBAAI,WAAW,AAAC,iEAGf,OAFA,cAAc,eAAe,aAC9B,KAIA,OAHC,UAAU,SACN,iDACA,kDACL;;;;;;;;;;;;AAGP;GA7DwB;;QACS,mIAAA,CAAA,WAAQ;;;KADjB", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}