"""
Test the registration endpoint
"""

import requests
import json

def test_registration():
    """Test user registration"""
    url = "http://localhost:8000/auth/register"
    
    import time
    timestamp = int(time.time())

    data = {
        "name": "Test User",
        "email": f"user{timestamp}@example.com",
        "password": "password123",
        "age": 25,
        "gender": "male"
    }
    
    try:
        print("🧪 Testing registration endpoint...")
        print(f"URL: {url}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, json=data, timeout=10)
        
        print(f"\n📊 Response:")
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Registration successful!")
            print(f"User ID: {result.get('user', {}).get('id')}")
            print(f"Token: {result.get('access_token', 'N/A')[:20]}...")
            return True
        else:
            print(f"❌ Registration failed!")
            try:
                error_detail = response.json()
                print(f"Error: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"Raw response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_registration()
