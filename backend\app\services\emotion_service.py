"""
Emotion detection service using Hugging Face models for text and images
"""

import asyncio
from typing import Dict, List, Union
import httpx
from transformers import pipeline
import torch
import cv2
import numpy as np
from PIL import Image
import io
import base64
from app.utils.config import settings

class EmotionDetectionService:
    """Service for detecting emotions from text using Hugging Face models"""
    
    def __init__(self):
        self.text_model_name = settings.emotion_model_name
        self.text_classifier = None
        self.image_classifier = None
        self.face_cascade = None
        self.is_text_initialized = False
        self.is_image_initialized = False

        # GoEmotions emotion labels (27 emotions + neutral)
        self.emotion_labels = [
            'admiration', 'amusement', 'anger', 'annoyance', 'approval', 'caring',
            'confusion', 'curiosity', 'desire', 'disappointment', 'disapproval',
            'disgust', 'embarrassment', 'excitement', 'fear', 'gratitude', 'grief',
            'joy', 'love', 'nervousness', 'optimism', 'pride', 'realization',
            'relief', 'remorse', 'sadness', 'surprise', 'neutral'
        ]

        # Image emotion labels (basic facial emotions)
        self.image_emotion_labels = [
            'angry', 'disgust', 'fear', 'happy', 'sad', 'surprise', 'neutral'
        ]
    
    async def initialize_text_model(self):
        """Initialize the text emotion detection model"""
        if self.is_text_initialized:
            return

        try:
            # Try to use local model first, fallback to API
            if torch.cuda.is_available():
                device = 0  # Use GPU if available
            else:
                device = -1  # Use CPU

            self.text_classifier = pipeline(
                "text-classification",
                model=self.text_model_name,
                device=device,
                return_all_scores=True
            )

            self.is_text_initialized = True
            print(f"Text emotion detection model {self.text_model_name} initialized successfully")

        except Exception as e:
            print(f"Failed to initialize text model: {e}")
            self.text_classifier = None

    async def initialize_image_model(self):
        """Initialize the image emotion detection model"""
        if self.is_image_initialized:
            return

        try:
            # Initialize face detection
            self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

            # For demo purposes, we'll use a simple emotion mapping
            # In production, you would use a proper facial emotion recognition model
            self.is_image_initialized = True
            print("Image emotion detection initialized successfully")

        except Exception as e:
            print(f"Failed to initialize image model: {e}")
            self.face_cascade = None
            
        except Exception as e:
            print(f"Failed to initialize local model: {e}")
            print("Will use Hugging Face API instead")
            self.classifier = None
    
    async def detect_emotions_local(self, text: str) -> Dict[str, float]:
        """Detect emotions using local model"""
        if not self.is_text_initialized:
            await self.initialize_text_model()
        
        if not self.text_classifier:
            raise Exception("Local model not available")
        
        # Run inference
        results = self.text_classifier(text)
        
        # Convert to dictionary format
        emotions_dict = {}
        for result in results[0]:  # results is a list with one element
            label = result['label'].lower()
            score = result['score']
            emotions_dict[label] = score
        
        return emotions_dict
    
    async def detect_emotions_api(self, text: str) -> Dict[str, float]:
        """Detect emotions using Hugging Face API"""
        if not settings.huggingface_api_key:
            raise Exception("Hugging Face API key not configured")
        
        headers = {
            "Authorization": f"Bearer {settings.huggingface_api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "inputs": text,
            "options": {"wait_for_model": True}
        }
        
        api_url = f"https://api-inference.huggingface.co/models/{self.model_name}"
        
        async with httpx.AsyncClient() as client:
            response = await client.post(api_url, headers=headers, json=payload)
            
            if response.status_code != 200:
                raise Exception(f"API request failed: {response.status_code} - {response.text}")
            
            results = response.json()
            
            # Convert to dictionary format
            emotions_dict = {}
            for result in results[0]:  # API returns list of lists
                label = result['label'].lower()
                score = result['score']
                emotions_dict[label] = score
            
            return emotions_dict
    
    async def detect_emotions_fallback(self, text: str) -> Dict[str, float]:
        """Fallback emotion detection using simple keyword matching"""
        print("Using fallback emotion detection")
        
        # Simple keyword-based emotion detection
        emotion_keywords = {
            'joy': ['happy', 'joy', 'excited', 'great', 'wonderful', 'amazing', 'fantastic'],
            'sadness': ['sad', 'depressed', 'down', 'unhappy', 'miserable', 'terrible'],
            'anger': ['angry', 'mad', 'furious', 'annoyed', 'frustrated', 'irritated'],
            'fear': ['scared', 'afraid', 'worried', 'anxious', 'nervous', 'terrified'],
            'surprise': ['surprised', 'shocked', 'amazed', 'astonished', 'unexpected'],
            'disgust': ['disgusted', 'gross', 'awful', 'horrible', 'nasty'],
            'love': ['love', 'adore', 'cherish', 'affection', 'romantic'],
            'gratitude': ['thank', 'grateful', 'appreciate', 'thankful'],
            'optimism': ['hope', 'optimistic', 'positive', 'confident', 'bright'],
            'neutral': ['okay', 'fine', 'normal', 'regular', 'usual']
        }
        
        text_lower = text.lower()
        emotions_dict = {}
        
        # Initialize all emotions with low scores
        for emotion in self.emotion_labels:
            emotions_dict[emotion] = 0.01
        
        # Check for keyword matches
        total_matches = 0
        for emotion, keywords in emotion_keywords.items():
            matches = sum(1 for keyword in keywords if keyword in text_lower)
            if matches > 0:
                emotions_dict[emotion] = min(0.8, 0.3 + (matches * 0.1))
                total_matches += matches
        
        # If no matches, default to neutral
        if total_matches == 0:
            emotions_dict['neutral'] = 0.7
        
        # Normalize scores to sum to 1
        total_score = sum(emotions_dict.values())
        if total_score > 0:
            emotions_dict = {k: v / total_score for k, v in emotions_dict.items()}
        
        return emotions_dict
    
    async def detect_emotions(self, text: str) -> Dict[str, float]:
        """
        Main method to detect emotions from text
        Tries local model first, then API, then fallback
        """
        try:
            # Try local model first
            return await self.detect_emotions_local(text)
        except Exception as e:
            print(f"Local model failed: {e}")
            
            try:
                # Try Hugging Face API
                return await self.detect_emotions_api(text)
            except Exception as e:
                print(f"API failed: {e}")
                
                # Use fallback method
                return await self.detect_emotions_fallback(text)
    
    def get_top_emotions(self, emotions_dict: Dict[str, float], top_k: int = 5) -> List[tuple]:
        """Get top K emotions with highest scores"""
        sorted_emotions = sorted(emotions_dict.items(), key=lambda x: x[1], reverse=True)
        return sorted_emotions[:top_k]
    
    def filter_emotions(self, emotions_dict: Dict[str, float], threshold: float = 0.1) -> Dict[str, float]:
        """Filter emotions below threshold"""
        return {emotion: score for emotion, score in emotions_dict.items() if score >= threshold}

    async def detect_emotions_from_image(self, image_data: Union[str, bytes]) -> Dict[str, float]:
        """Detect emotions from image (face detection + emotion analysis)"""
        await self.initialize_image_model()

        try:
            # Convert image data to OpenCV format
            if isinstance(image_data, str):
                # Base64 encoded image
                image_bytes = base64.b64decode(image_data)
            else:
                image_bytes = image_data

            # Convert to PIL Image then to numpy array
            pil_image = Image.open(io.BytesIO(image_bytes))
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

            # Convert to grayscale for face detection
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

            # Detect faces
            if self.face_cascade is None:
                return await self.detect_emotions_fallback_image()

            faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)

            if len(faces) == 0:
                # No faces detected, return neutral emotion
                return {
                    'neutral': 0.8,
                    'happy': 0.1,
                    'sad': 0.1
                }

            # For demo purposes, simulate emotion detection based on image properties
            # In production, you would use a proper facial emotion recognition model
            return await self.analyze_facial_features(cv_image, faces)

        except Exception as e:
            print(f"Error in image emotion detection: {e}")
            return await self.detect_emotions_fallback_image()

    async def analyze_facial_features(self, image: np.ndarray, faces: np.ndarray) -> Dict[str, float]:
        """Analyze facial features to determine emotions (simplified demo version)"""
        try:
            # This is a simplified demo implementation
            # In production, you would use a proper facial emotion recognition model

            # Get the largest face
            largest_face = max(faces, key=lambda face: face[2] * face[3])
            x, y, w, h = largest_face

            # Extract face region
            face_roi = image[y:y+h, x:x+w]

            # Simple heuristic based on image properties (demo only)
            # Calculate brightness and contrast as basic features
            gray_face = cv2.cvtColor(face_roi, cv2.COLOR_BGR2GRAY)
            brightness = np.mean(gray_face)
            contrast = np.std(gray_face)

            # Map to emotions (this is a very simplified approach)
            emotions = {}

            if brightness > 120:
                emotions['happy'] = min(0.8, brightness / 150)
                emotions['joy'] = min(0.6, brightness / 180)
            else:
                emotions['sad'] = min(0.7, (150 - brightness) / 150)
                emotions['neutral'] = min(0.5, (150 - brightness) / 200)

            if contrast > 50:
                emotions['surprise'] = min(0.6, contrast / 100)
            else:
                emotions['calm'] = min(0.5, (100 - contrast) / 100)

            # Normalize scores
            total_score = sum(emotions.values())
            if total_score > 0:
                emotions = {k: v / total_score for k, v in emotions.items()}

            # Ensure we have at least some emotions
            if not emotions:
                emotions = {'neutral': 0.7, 'happy': 0.2, 'calm': 0.1}

            return emotions

        except Exception as e:
            print(f"Error analyzing facial features: {e}")
            return await self.detect_emotions_fallback_image()

    async def detect_emotions_fallback_image(self) -> Dict[str, float]:
        """Fallback emotion detection for images"""
        # Return a neutral emotion distribution
        return {
            'neutral': 0.6,
            'happy': 0.2,
            'calm': 0.1,
            'surprise': 0.1
        }
