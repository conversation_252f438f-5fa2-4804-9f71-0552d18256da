# Emotion Detection System

A comprehensive web application for detecting emotions, age, and gender from text inputs with features for daily emotion tracking, activity suggestions, and user engagement.

## Architecture

- **Frontend**: Next.js with Tailwind CSS
- **Backend**: FastAPI with PostgreSQL
- **ML**: Hugging Face Transformers (GoEmotions dataset)
- **Notifications**: Firebase Cloud Messaging

## Project Structure

```
emotion-detection-system/
├── frontend/                 # Next.js frontend application
│   ├── components/          # React components
│   ├── pages/              # Next.js pages
│   ├── styles/             # CSS and Tailwind styles
│   └── utils/              # Utility functions
├── backend/                 # FastAPI backend application
│   ├── app/                # Main application code
│   │   ├── api/            # API endpoints
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── alembic/            # Database migrations
│   └── requirements.txt    # Python dependencies
└── README.md               # This file
```

## Features

### Frontend Features
- **Authentication**: User registration and login with JWT
- **Dashboard**: Emotion trends, daily scores, and activity suggestions
- **Text Input**: Real-time emotion detection from text
- **Emotion Diary**: Manual emotion logging with notes
- **Monthly Reports**: Downloadable PDF reports with insights
- **Activity Suggestions**: Personalized recommendations
- **Weekly Challenges**: Gamified emotional well-being tasks
- **Notification Settings**: Customizable push notifications

### Backend Features
- **User Management**: Registration, authentication, and profiles
- **Emotion Detection**: ML-powered text analysis using GoEmotions
- **Data Analytics**: Trend analysis and insight generation
- **Activity Engine**: Personalized activity recommendations
- **Challenge System**: Weekly challenges and progress tracking
- **Notification Service**: Firebase-based push notifications

## Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Python 3.8+
- PostgreSQL 12+ (or SQLite for development)
- Git

### Quick Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd emotion-detection-system
   ```

2. **Backend Setup**
   ```bash
   cd backend

   # Create virtual environment (recommended)
   python -m venv venv

   # Activate virtual environment
   # Windows:
   venv\Scripts\activate
   # macOS/Linux:
   source venv/bin/activate

   # Install dependencies
   pip install -r requirements.txt

   # Setup database (PostgreSQL)
   createdb emotion_detection

   # Or use SQLite for development (modify DATABASE_URL in .env)
   # DATABASE_URL=sqlite:///./emotion_detection.db

   # Start the backend server
   python start.py
   ```

3. **Frontend Setup** (in a new terminal)
   ```bash
   cd frontend

   # Install dependencies
   npm install

   # Start the development server
   npm run dev
   ```

4. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Detailed Setup Instructions

#### Database Configuration

**Option 1: PostgreSQL (Recommended for production)**
```bash
# Install PostgreSQL and create database
createdb emotion_detection

# Update backend/.env with your PostgreSQL credentials
DATABASE_URL=postgresql://username:password@localhost/emotion_detection
```

**Option 2: SQLite (Quick development setup)**
```bash
# Update backend/.env to use SQLite
DATABASE_URL=sqlite:///./emotion_detection.db
```

#### Environment Configuration

**Backend (.env)**
```bash
cd backend
cp .env.example .env
# Edit .env with your configuration
```

**Frontend (.env.local)**
```bash
cd frontend
# Create .env.local file
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### Testing the Application

1. **Start both servers** (backend on :8000, frontend on :3000)

2. **Create an account**
   - Go to http://localhost:3000
   - Click "Sign up" and create a new account
   - Fill in your details (age and gender are optional)

3. **Test emotion detection**
   - Click "Detect Emotion" from the dashboard
   - Enter text like "I'm feeling really happy today!"
   - View the emotion analysis and activity suggestions

4. **Explore features**
   - Add manual diary entries
   - View your emotion history
   - Check out the dashboard analytics

### Troubleshooting

**Backend Issues:**
- Ensure PostgreSQL is running and database exists
- Check Python virtual environment is activated
- Verify all dependencies are installed: `pip install -r requirements.txt`
- Check backend logs for specific error messages

**Frontend Issues:**
- Ensure Node.js 18+ is installed
- Clear npm cache: `npm cache clean --force`
- Delete node_modules and reinstall: `rm -rf node_modules && npm install`
- Check browser console for JavaScript errors

**Database Issues:**
- For PostgreSQL: Ensure service is running and credentials are correct
- For SQLite: Ensure write permissions in the backend directory
- Check DATABASE_URL format in .env file

**API Connection Issues:**
- Verify backend is running on port 8000
- Check CORS settings in backend/app/main.py
- Ensure NEXT_PUBLIC_API_URL is set correctly in frontend/.env.local

## Environment Variables

### Frontend (.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
```

### Backend (.env)
```
DATABASE_URL=postgresql://user:password@localhost/emotion_detection
SECRET_KEY=your_secret_key
HUGGINGFACE_API_KEY=your_huggingface_api_key
FIREBASE_CREDENTIALS_PATH=path/to/firebase-credentials.json
```

## API Documentation

Once the backend is running, visit `http://localhost:8000/docs` for interactive API documentation.

## Technology Stack

### Frontend
- **Next.js**: React framework with SSR
- **Tailwind CSS**: Utility-first CSS framework
- **Chart.js**: Data visualization
- **Axios**: HTTP client
- **jsPDF**: PDF generation

### Backend
- **FastAPI**: Modern Python web framework
- **PostgreSQL**: Relational database
- **SQLAlchemy**: ORM
- **Alembic**: Database migrations
- **Hugging Face**: ML model integration
- **Firebase**: Push notifications

### Machine Learning
- **Model**: bsingh/roberta_goEmotion (GoEmotions dataset)
- **Emotions**: 27 specific emotions + Neutral
- **Labels**: Positive, Negative, Neutral classification

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
