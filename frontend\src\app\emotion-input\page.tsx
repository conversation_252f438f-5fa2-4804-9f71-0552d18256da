'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { emotionAPI, activitiesAPI, isDemoMode } from '@/utils/api';
import type { EmotionResult, Activity } from '@/utils/api';

export default function EmotionInputPage() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [inputType, setInputType] = useState<'text' | 'image'>('text');
  const [text, setText] = useState('');
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [result, setResult] = useState<EmotionResult | null>(null);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth');
    }
  }, [isAuthenticated, router]);

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file');
        return;
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setError('Image size must be less than 5MB');
        return;
      }

      setSelectedImage(file);
      setError('');

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (inputType === 'text' && !text.trim()) {
      setError('Please enter some text to analyze');
      return;
    }

    if (inputType === 'image' && !selectedImage) {
      setError('Please select an image to analyze');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      let emotionResult: EmotionResult;

      if (isDemoMode()) {
        // Generate mock emotion result for demo
        const mockEmotions = ['happy', 'sad', 'angry', 'fear', 'surprise', 'neutral'];
        const primaryEmotion = mockEmotions[Math.floor(Math.random() * mockEmotions.length)];

        emotionResult = {
          text: inputType === 'text' ? text.trim() : `Image: ${selectedImage?.name}`,
          primary_label: primaryEmotion,
          confidence: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
          detected_emotions: {
            [primaryEmotion]: Math.random() * 0.3 + 0.7,
            [mockEmotions[Math.floor(Math.random() * mockEmotions.length)]]: Math.random() * 0.3 + 0.2,
            [mockEmotions[Math.floor(Math.random() * mockEmotions.length)]]: Math.random() * 0.2 + 0.1,
          },
          timestamp: new Date().toISOString(),
          user_id: 1,
          input_type: inputType
        };
      } else {
        // Real API calls
        if (inputType === 'text') {
          emotionResult = await emotionAPI.detectEmotion(text.trim());
        } else {
          // Handle image emotion detection
          if (selectedImage) {
            emotionResult = await emotionAPI.detectEmotionFromImage(selectedImage);
          } else {
            throw new Error('No image selected');
          }
        }
      }

      setResult(emotionResult);

      // Get activity suggestions based on detected emotion
      if (isDemoMode()) {
        const mockActivities: Activity[] = [
          {
            id: 1,
            emotion_category: 'general',
            activity_description: inputType === 'image'
              ? 'Try some facial exercises to improve your mood'
              : 'Take a few deep breaths and practice mindfulness',
            link: null,
            category: 'mindfulness',
            duration_minutes: 5,
            difficulty_level: 'easy'
          },
          {
            id: 2,
            emotion_category: 'general',
            activity_description: inputType === 'image'
              ? 'Look in the mirror and practice positive affirmations'
              : 'Go for a short walk outside',
            link: null,
            category: inputType === 'image' ? 'self-care' : 'exercise',
            duration_minutes: 15,
            difficulty_level: 'easy'
          }
        ];
        setActivities(mockActivities);
      } else {
        const activitySuggestions = await activitiesAPI.getSuggestions(emotionResult.primary_label);
        setActivities(activitySuggestions.activities.slice(0, 5));
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || `Failed to analyze emotion from ${inputType}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setText('');
    setSelectedImage(null);
    setImagePreview(null);
    setResult(null);
    setActivities([]);
    setError('');
  };

  const getEmotionColor = (label: string) => {
    const colors: Record<string, string> = {
      positive: 'text-green-600 bg-green-100 border-green-200',
      negative: 'text-red-600 bg-red-100 border-red-200',
      neutral: 'text-gray-600 bg-gray-100 border-gray-200',
    };
    return colors[label] || 'text-gray-600 bg-gray-100 border-gray-200';
  };

  const getTopEmotions = (emotions: Record<string, number>) => {
    return Object.entries(emotions)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Emotion Detection</h1>
              <p className="text-gray-600">Analyze your emotions from text</p>
            </div>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-gray-500 hover:text-gray-700 text-sm font-medium"
            >
              ← Back to Dashboard
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Input Type Selector */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Choose Detection Method
              </h3>
              <div className="flex space-x-4 mb-6">
                <button
                  type="button"
                  onClick={() => {
                    setInputType('text');
                    setError('');
                  }}
                  className={`flex-1 py-3 px-4 rounded-lg border-2 transition-colors ${
                    inputType === 'text'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">📝</div>
                    <div className="font-medium">Text Analysis</div>
                    <div className="text-sm opacity-75">Analyze emotions from written text</div>
                  </div>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setInputType('image');
                    setError('');
                  }}
                  className={`flex-1 py-3 px-4 rounded-lg border-2 transition-colors ${
                    inputType === 'image'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">📷</div>
                    <div className="font-medium">Image Analysis</div>
                    <div className="text-sm opacity-75">Detect emotions from facial expressions</div>
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Input Form */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <form onSubmit={handleSubmit}>
                {inputType === 'text' ? (
                  <div className="mb-4">
                    <label htmlFor="emotion-text" className="block text-sm font-medium text-gray-700 mb-2">
                      How are you feeling? Describe your emotions in text:
                    </label>
                    <textarea
                      id="emotion-text"
                      value={text}
                      onChange={(e) => setText(e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., I'm feeling really happy today because I accomplished my goals..."
                      disabled={isLoading}
                    />
                  </div>
                ) : (
                  <div className="mb-4">
                    <label htmlFor="emotion-image" className="block text-sm font-medium text-gray-700 mb-2">
                      Upload an image to analyze facial emotions:
                    </label>
                    <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                      <div className="space-y-1 text-center">
                        {imagePreview ? (
                          <div className="mb-4">
                            <img
                              src={imagePreview}
                              alt="Preview"
                              className="mx-auto h-32 w-32 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => {
                                setSelectedImage(null);
                                setImagePreview(null);
                              }}
                              className="mt-2 text-sm text-red-600 hover:text-red-800"
                            >
                              Remove image
                            </button>
                          </div>
                        ) : (
                          <>
                            <svg
                              className="mx-auto h-12 w-12 text-gray-400"
                              stroke="currentColor"
                              fill="none"
                              viewBox="0 0 48 48"
                            >
                              <path
                                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                            <div className="flex text-sm text-gray-600">
                              <label
                                htmlFor="emotion-image"
                                className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                              >
                                <span>Upload a file</span>
                                <input
                                  id="emotion-image"
                                  name="emotion-image"
                                  type="file"
                                  accept="image/*"
                                  className="sr-only"
                                  onChange={handleImageSelect}
                                  disabled={isLoading}
                                />
                              </label>
                              <p className="pl-1">or drag and drop</p>
                            </div>
                            <p className="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {error && (
                  <div className="mb-4 rounded-md bg-red-50 p-4">
                    <div className="text-sm text-red-700">{error}</div>
                  </div>
                )}

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={isLoading || !text.trim()}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Analyzing...
                      </div>
                    ) : (
                      'Analyze Emotion'
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={handleClear}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Clear
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Primary Emotion */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Primary Emotion</h3>
                  <div className="flex items-center justify-between">
                    <div className={`inline-flex px-4 py-2 rounded-lg border ${getEmotionColor(result.primary_label)}`}>
                      <span className="text-lg font-semibold capitalize">{result.primary_label}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">
                        {Math.round(result.confidence_score * 100)}%
                      </div>
                      <div className="text-sm text-gray-500">Confidence</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Detailed Emotions */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Detailed Emotion Analysis</h3>
                  <div className="space-y-3">
                    {getTopEmotions(result.emotions).map(([emotion, score]) => (
                      <div key={emotion} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-900 capitalize w-24">
                            {emotion}
                          </span>
                          <div className="flex-1 mx-4">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${score * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                        <span className="text-sm text-gray-600 w-12 text-right">
                          {Math.round(score * 100)}%
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Activity Suggestions */}
              {activities.length > 0 && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Suggested Activities for {result.primary_label} emotions
                    </h3>
                    <div className="space-y-4">
                      {activities.map((activity) => (
                        <div key={activity.id} className="border-l-4 border-blue-400 pl-4 py-2">
                          <div className="text-sm font-medium text-gray-900">
                            {activity.activity_description}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                              {activity.category}
                            </span>
                            {activity.duration_minutes && (
                              <span className="mr-2">{activity.duration_minutes} min</span>
                            )}
                            <span className="capitalize">{activity.difficulty_level}</span>
                          </div>
                          {activity.link && (
                            <a
                              href={activity.link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 text-xs mt-1 inline-block"
                            >
                              Learn more →
                            </a>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <button
                  onClick={() => router.push('/diary')}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Add to Diary
                </button>
                <button
                  onClick={() => router.push('/dashboard')}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Back to Dashboard
                </button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
