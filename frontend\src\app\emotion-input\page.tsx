'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { emotionAPI, activitiesAPI } from '@/utils/api';
import type { EmotionResult, Activity } from '@/utils/api';

export default function EmotionInputPage() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [text, setText] = useState('');
  const [result, setResult] = useState<EmotionResult | null>(null);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth');
    }
  }, [isAuthenticated, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!text.trim()) {
      setError('Please enter some text to analyze');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Detect emotion
      const emotionResult = await emotionAPI.detectEmotion(text.trim());
      setResult(emotionResult);

      // Get activity suggestions based on detected emotion
      const activitySuggestions = await activitiesAPI.getSuggestions(emotionResult.primary_label);
      setActivities(activitySuggestions.activities.slice(0, 5));

    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to analyze emotion');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setText('');
    setResult(null);
    setActivities([]);
    setError('');
  };

  const getEmotionColor = (label: string) => {
    const colors: Record<string, string> = {
      positive: 'text-green-600 bg-green-100 border-green-200',
      negative: 'text-red-600 bg-red-100 border-red-200',
      neutral: 'text-gray-600 bg-gray-100 border-gray-200',
    };
    return colors[label] || 'text-gray-600 bg-gray-100 border-gray-200';
  };

  const getTopEmotions = (emotions: Record<string, number>) => {
    return Object.entries(emotions)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Emotion Detection</h1>
              <p className="text-gray-600">Analyze your emotions from text</p>
            </div>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-gray-500 hover:text-gray-700 text-sm font-medium"
            >
              ← Back to Dashboard
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Input Form */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label htmlFor="emotion-text" className="block text-sm font-medium text-gray-700 mb-2">
                    How are you feeling? Describe your emotions in text:
                  </label>
                  <textarea
                    id="emotion-text"
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., I'm feeling really happy today because I accomplished my goals..."
                    disabled={isLoading}
                  />
                </div>

                {error && (
                  <div className="mb-4 rounded-md bg-red-50 p-4">
                    <div className="text-sm text-red-700">{error}</div>
                  </div>
                )}

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={isLoading || !text.trim()}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Analyzing...
                      </div>
                    ) : (
                      'Analyze Emotion'
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={handleClear}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Clear
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Primary Emotion */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Primary Emotion</h3>
                  <div className="flex items-center justify-between">
                    <div className={`inline-flex px-4 py-2 rounded-lg border ${getEmotionColor(result.primary_label)}`}>
                      <span className="text-lg font-semibold capitalize">{result.primary_label}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">
                        {Math.round(result.confidence_score * 100)}%
                      </div>
                      <div className="text-sm text-gray-500">Confidence</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Detailed Emotions */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Detailed Emotion Analysis</h3>
                  <div className="space-y-3">
                    {getTopEmotions(result.emotions).map(([emotion, score]) => (
                      <div key={emotion} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-900 capitalize w-24">
                            {emotion}
                          </span>
                          <div className="flex-1 mx-4">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${score * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                        <span className="text-sm text-gray-600 w-12 text-right">
                          {Math.round(score * 100)}%
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Activity Suggestions */}
              {activities.length > 0 && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Suggested Activities for {result.primary_label} emotions
                    </h3>
                    <div className="space-y-4">
                      {activities.map((activity) => (
                        <div key={activity.id} className="border-l-4 border-blue-400 pl-4 py-2">
                          <div className="text-sm font-medium text-gray-900">
                            {activity.activity_description}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                              {activity.category}
                            </span>
                            {activity.duration_minutes && (
                              <span className="mr-2">{activity.duration_minutes} min</span>
                            )}
                            <span className="capitalize">{activity.difficulty_level}</span>
                          </div>
                          {activity.link && (
                            <a
                              href={activity.link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 text-xs mt-1 inline-block"
                            >
                              Learn more →
                            </a>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <button
                  onClick={() => router.push('/diary')}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Add to Diary
                </button>
                <button
                  onClick={() => router.push('/dashboard')}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Back to Dashboard
                </button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
