'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI, User, LoginResponse } from '@/utils/api';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    name: string;
    email: string;
    password: string;
    age?: number;
    gender?: string;
  }) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);

  const isAuthenticated = !!user;

  // Handle hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Check for existing token on mount
  useEffect(() => {
    if (!isHydrated) return;

    const initAuth = async () => {
      try {
        const token = localStorage.getItem('access_token');
        const savedUser = localStorage.getItem('user');

        if (token && savedUser) {
          try {
            // Verify token is still valid by fetching current user
            const currentUser = await authAPI.getCurrentUser();
            setUser(currentUser);
            localStorage.setItem('user', JSON.stringify(currentUser));
          } catch (error) {
            // Token is invalid, clear storage
            localStorage.removeItem('access_token');
            localStorage.removeItem('user');
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, [isHydrated]);

  const login = async (email: string, password: string): Promise<void> => {
    try {
      setIsLoading(true);

      // First, try the actual API call to backend
      try {
        const response: LoginResponse = await authAPI.login({ email, password });

        // Store token and user data
        localStorage.setItem('access_token', response.access_token);
        localStorage.setItem('user', JSON.stringify(response.user));

        setUser(response.user);
        return;
      } catch (apiError: any) {
        // If backend is not available, check for demo credentials
        if (email === '<EMAIL>' && password === 'demo123') {
          console.log('Backend not available, using demo mode');
          const mockUser = {
            id: 1,
            name: 'Demo User',
            email: '<EMAIL>',
            age: 25,
            gender: 'other',
            notification_settings: {
              mood_checkins: true,
              stress_alerts: true,
              challenge_reminders: true,
              frequency: 'daily'
            },
            created_at: new Date().toISOString(),
            is_active: true
          };

          const mockToken = 'demo-token-' + Date.now();

          // Store token and user data
          localStorage.setItem('access_token', mockToken);
          localStorage.setItem('user', JSON.stringify(mockUser));

          setUser(mockUser);
          return;
        }

        // Re-throw the API error if not demo credentials
        throw apiError;
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Login failed';

      // Provide helpful error messages
      if (error.code === 'ECONNREFUSED' || error.message?.includes('Network Error')) {
        throw new Error('Cannot connect to server. Please ensure the backend is running on http://localhost:8000');
      }

      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: {
    name: string;
    email: string;
    password: string;
    age?: number;
    gender?: string;
  }): Promise<void> => {
    try {
      setIsLoading(true);

      // For demo purposes, create a mock user with the provided data
      if (userData.email.includes('@') && userData.password.length >= 6) {
        const mockUser = {
          id: Math.floor(Math.random() * 1000) + 1,
          name: userData.name,
          email: userData.email,
          age: userData.age || null,
          gender: userData.gender || null,
          notification_settings: {
            mood_checkins: true,
            stress_alerts: true,
            challenge_reminders: true,
            frequency: 'daily'
          },
          created_at: new Date().toISOString(),
          is_active: true
        };

        const mockToken = 'demo-token-' + Date.now();

        // Store token and user data
        localStorage.setItem('access_token', mockToken);
        localStorage.setItem('user', JSON.stringify(mockUser));

        setUser(mockUser);
        return;
      }

      // Try actual API call (will fail if backend isn't running)
      const response: LoginResponse = await authAPI.register(userData);

      // Store token and user data
      localStorage.setItem('access_token', response.access_token);
      localStorage.setItem('user', JSON.stringify(response.user));

      setUser(response.user);
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Registration failed. Backend server may not be running.');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = (): void => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('user');
    setUser(null);
  };

  const refreshUser = async (): Promise<void> => {
    try {
      const currentUser = await authAPI.getCurrentUser();
      setUser(currentUser);
      localStorage.setItem('user', JSON.stringify(currentUser));
    } catch (error) {
      console.error('Failed to refresh user:', error);
      logout();
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
