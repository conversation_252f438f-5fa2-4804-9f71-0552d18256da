{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/app/reports/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { reportsAPI, isDemoMode, mockData } from '@/utils/api';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n} from 'chart.js';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement\n);\n\ninterface EmotionStats {\n  total_logs: number;\n  emotion_distribution: { [key: string]: number };\n  weekly_average: number;\n  most_common_emotion: string;\n}\n\nexport default function ReportsPage() {\n  const { isAuthenticated } = useAuth();\n  const router = useRouter();\n  const [weeklyTrend, setWeeklyTrend] = useState<any[]>([]);\n  const [monthlyData, setMonthlyData] = useState<any[]>([]);\n  const [emotionStats, setEmotionStats] = useState<EmotionStats | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('week');\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/auth');\n      return;\n    }\n\n    const fetchReportsData = async () => {\n      try {\n        setIsLoading(true);\n        \n        if (isDemoMode()) {\n          // Use mock data\n          setWeeklyTrend(mockData.weeklyTrend);\n          setMonthlyData(generateMockMonthlyData());\n          setEmotionStats(generateMockEmotionStats());\n        } else {\n          // Fetch real data\n          const [weeklyRes, monthlyRes, statsRes] = await Promise.all([\n            reportsAPI.getWeeklyTrend(),\n            reportsAPI.getMonthlyReport(),\n            reportsAPI.getEmotionStats()\n          ]);\n          \n          setWeeklyTrend(weeklyRes.weekly_trend);\n          setMonthlyData(monthlyRes.monthly_data);\n          setEmotionStats(statsRes);\n        }\n      } catch (error) {\n        console.error('Error fetching reports data:', error);\n        // Fallback to mock data\n        setWeeklyTrend(mockData.weeklyTrend);\n        setMonthlyData(generateMockMonthlyData());\n        setEmotionStats(generateMockEmotionStats());\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchReportsData();\n  }, [isAuthenticated, router]);\n\n  const generateMockMonthlyData = () => {\n    const data = [];\n    for (let i = 29; i >= 0; i--) {\n      const date = new Date();\n      date.setDate(date.getDate() - i);\n      data.push({\n        date: date.toISOString().split('T')[0],\n        emotion_score: Math.floor(Math.random() * 40) + 50,\n        total_logs: Math.floor(Math.random() * 8) + 1,\n        dominant_emotion: ['happy', 'neutral', 'sad', 'excited', 'calm'][Math.floor(Math.random() * 5)]\n      });\n    }\n    return data;\n  };\n\n  const generateMockEmotionStats = (): EmotionStats => ({\n    total_logs: 156,\n    emotion_distribution: {\n      happy: 45,\n      neutral: 32,\n      sad: 18,\n      excited: 25,\n      calm: 20,\n      anxious: 16\n    },\n    weekly_average: 75.2,\n    most_common_emotion: 'happy'\n  });\n\n  const getChartData = () => {\n    const data = selectedPeriod === 'week' ? weeklyTrend : monthlyData;\n    \n    return {\n      labels: data.map(item => {\n        const date = new Date(item.date);\n        return selectedPeriod === 'week' \n          ? date.toLocaleDateString('en-US', { weekday: 'short' })\n          : date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });\n      }),\n      datasets: [\n        {\n          label: 'Emotion Score',\n          data: data.map(item => item.emotion_score),\n          borderColor: 'rgb(59, 130, 246)',\n          backgroundColor: 'rgba(59, 130, 246, 0.1)',\n          tension: 0.4,\n        },\n      ],\n    };\n  };\n\n  const getEmotionDistributionData = () => {\n    if (!emotionStats) return { labels: [], datasets: [] };\n\n    const emotions = Object.keys(emotionStats.emotion_distribution);\n    const counts = Object.values(emotionStats.emotion_distribution);\n    \n    return {\n      labels: emotions.map(emotion => emotion.charAt(0).toUpperCase() + emotion.slice(1)),\n      datasets: [\n        {\n          data: counts,\n          backgroundColor: [\n            '#10B981', // green\n            '#3B82F6', // blue\n            '#EF4444', // red\n            '#F59E0B', // yellow\n            '#8B5CF6', // purple\n            '#EC4899', // pink\n          ],\n          borderWidth: 2,\n          borderColor: '#ffffff',\n        },\n      ],\n    };\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n      title: {\n        display: true,\n        text: `Emotion Trends - ${selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)}`,\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        max: 100,\n      },\n    },\n  };\n\n  const doughnutOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'right' as const,\n      },\n      title: {\n        display: true,\n        text: 'Emotion Distribution',\n      },\n    },\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Reports & Analytics</h1>\n              <p className=\"text-gray-600\">Insights into your emotional patterns</p>\n            </div>\n            <button\n              onClick={() => router.push('/dashboard')}\n              className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n            >\n              ← Back to Dashboard\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Stats Overview */}\n          {emotionStats && (\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"text-2xl\">📊</div>\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Logs</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">{emotionStats.total_logs}</dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"text-2xl\">📈</div>\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Weekly Average</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">{emotionStats.weekly_average}</dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"text-2xl\">😊</div>\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Most Common</dt>\n                        <dd className=\"text-lg font-medium text-gray-900 capitalize\">{emotionStats.most_common_emotion}</dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"text-2xl\">🎯</div>\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Emotions Tracked</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">{Object.keys(emotionStats.emotion_distribution).length}</dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Period Selector */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900\">Emotion Trends</h3>\n                <div className=\"flex space-x-2\">\n                  {(['week', 'month'] as const).map((period) => (\n                    <button\n                      key={period}\n                      onClick={() => setSelectedPeriod(period)}\n                      className={`px-3 py-1 rounded-md text-sm font-medium ${\n                        selectedPeriod === period\n                          ? 'bg-blue-600 text-white'\n                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                      }`}\n                    >\n                      {period.charAt(0).toUpperCase() + period.slice(1)}\n                    </button>\n                  ))}\n                </div>\n              </div>\n              <div className=\"h-96\">\n                <Line data={getChartData()} options={chartOptions} />\n              </div>\n            </div>\n          </div>\n\n          {/* Charts Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Emotion Distribution */}\n            <div className=\"bg-white shadow rounded-lg\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Emotion Distribution</h3>\n                <div className=\"h-80\">\n                  <Doughnut data={getEmotionDistributionData()} options={doughnutOptions} />\n                </div>\n              </div>\n            </div>\n\n            {/* Daily Activity */}\n            <div className=\"bg-white shadow rounded-lg\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Daily Activity</h3>\n                <div className=\"h-80\">\n                  <Bar \n                    data={{\n                      labels: weeklyTrend.map(item => new Date(item.date).toLocaleDateString('en-US', { weekday: 'short' })),\n                      datasets: [{\n                        label: 'Logs per Day',\n                        data: weeklyTrend.map(item => item.total_logs),\n                        backgroundColor: 'rgba(59, 130, 246, 0.6)',\n                        borderColor: 'rgb(59, 130, 246)',\n                        borderWidth: 1,\n                      }]\n                    }}\n                    options={{\n                      responsive: true,\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'top' as const,\n                        },\n                        title: {\n                          display: true,\n                          text: 'Emotion Logs per Day',\n                        },\n                      },\n                      scales: {\n                        y: {\n                          beginAtZero: true,\n                        },\n                      },\n                    }}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAYA;;;AAlBA;;;;;;;AAoBA,+JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,+JAAA,CAAA,gBAAa,EACb,+JAAA,CAAA,cAAW,EACX,+JAAA,CAAA,eAAY,EACZ,+JAAA,CAAA,cAAW,EACX,+JAAA,CAAA,aAAU,EACV,+JAAA,CAAA,QAAK,EACL,+JAAA,CAAA,UAAO,EACP,+JAAA,CAAA,SAAM,EACN,+JAAA,CAAA,aAAU;AAUG,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAEhF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM;0DAAmB;oBACvB,IAAI;wBACF,aAAa;wBAEb,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;4BAChB,gBAAgB;4BAChB,eAAe,sHAAA,CAAA,WAAQ,CAAC,WAAW;4BACnC,eAAe;4BACf,gBAAgB;wBAClB,OAAO;4BACL,kBAAkB;4BAClB,MAAM,CAAC,WAAW,YAAY,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAC1D,sHAAA,CAAA,aAAU,CAAC,cAAc;gCACzB,sHAAA,CAAA,aAAU,CAAC,gBAAgB;gCAC3B,sHAAA,CAAA,aAAU,CAAC,eAAe;6BAC3B;4BAED,eAAe,UAAU,YAAY;4BACrC,eAAe,WAAW,YAAY;4BACtC,gBAAgB;wBAClB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;wBAC9C,wBAAwB;wBACxB,eAAe,sHAAA,CAAA,WAAQ,CAAC,WAAW;wBACnC,eAAe;wBACf,gBAAgB;oBAClB,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;gCAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,0BAA0B;QAC9B,MAAM,OAAO,EAAE;QACf,IAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAK;YAC5B,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;YAC9B,KAAK,IAAI,CAAC;gBACR,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtC,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAChD,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;gBAC5C,kBAAkB;oBAAC;oBAAS;oBAAW;oBAAO;oBAAW;iBAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;YACjG;QACF;QACA,OAAO;IACT;IAEA,MAAM,2BAA2B,IAAoB,CAAC;YACpD,YAAY;YACZ,sBAAsB;gBACpB,OAAO;gBACP,SAAS;gBACT,KAAK;gBACL,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;YACA,gBAAgB;YAChB,qBAAqB;QACvB,CAAC;IAED,MAAM,eAAe;QACnB,MAAM,OAAO,mBAAmB,SAAS,cAAc;QAEvD,OAAO;YACL,QAAQ,KAAK,GAAG,CAAC,CAAA;gBACf,MAAM,OAAO,IAAI,KAAK,KAAK,IAAI;gBAC/B,OAAO,mBAAmB,SACtB,KAAK,kBAAkB,CAAC,SAAS;oBAAE,SAAS;gBAAQ,KACpD,KAAK,kBAAkB,CAAC,SAAS;oBAAE,OAAO;oBAAS,KAAK;gBAAU;YACxE;YACA,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,aAAa;oBACzC,aAAa;oBACb,iBAAiB;oBACjB,SAAS;gBACX;aACD;QACH;IACF;IAEA,MAAM,6BAA6B;QACjC,IAAI,CAAC,cAAc,OAAO;YAAE,QAAQ,EAAE;YAAE,UAAU,EAAE;QAAC;QAErD,MAAM,WAAW,OAAO,IAAI,CAAC,aAAa,oBAAoB;QAC9D,MAAM,SAAS,OAAO,MAAM,CAAC,aAAa,oBAAoB;QAE9D,OAAO;YACL,QAAQ,SAAS,GAAG,CAAC,CAAA,UAAW,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC;YAChF,UAAU;gBACR;oBACE,MAAM;oBACN,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,aAAa;oBACb,aAAa;gBACf;aACD;QACH;IACF;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM,AAAC,oBAAoF,OAAjE,eAAe,MAAM,CAAC,GAAG,WAAW,KAAK,eAAe,KAAK,CAAC;YAC1F;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,KAAK;YACP;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,8BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,6LAAC;gEAAG,WAAU;0EAAqC,aAAa,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOpF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,6LAAC;gEAAG,WAAU;0EAAqC,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOxF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,6LAAC;gEAAG,WAAU;0EAAgD,aAAa,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOxG,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,6LAAC;gEAAG,WAAU;0EAAqC,OAAO,IAAI,CAAC,aAAa,oBAAoB,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUtH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,6LAAC;gDAAI,WAAU;0DACZ,AAAC;oDAAC;oDAAQ;iDAAQ,CAAW,GAAG,CAAC,CAAC,uBACjC,6LAAC;wDAEC,SAAS,IAAM,kBAAkB;wDACjC,WAAW,AAAC,4CAIX,OAHC,mBAAmB,SACf,2BACA;kEAGL,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;uDAR1C;;;;;;;;;;;;;;;;kDAab,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yJAAA,CAAA,OAAI;4CAAC,MAAM;4CAAgB,SAAS;;;;;;;;;;;;;;;;;;;;;;sCAM3C,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yJAAA,CAAA,WAAQ;oDAAC,MAAM;oDAA8B,SAAS;;;;;;;;;;;;;;;;;;;;;;8CAM7D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yJAAA,CAAA,MAAG;oDACF,MAAM;wDACJ,QAAQ,YAAY,GAAG,CAAC,CAAA,OAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB,CAAC,SAAS;gEAAE,SAAS;4DAAQ;wDACnG,UAAU;4DAAC;gEACT,OAAO;gEACP,MAAM,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,UAAU;gEAC7C,iBAAiB;gEACjB,aAAa;gEACb,aAAa;4DACf;yDAAE;oDACJ;oDACA,SAAS;wDACP,YAAY;wDACZ,qBAAqB;wDACrB,SAAS;4DACP,QAAQ;gEACN,UAAU;4DACZ;4DACA,OAAO;gEACL,SAAS;gEACT,MAAM;4DACR;wDACF;wDACA,QAAQ;4DACN,GAAG;gEACD,aAAa;4DACf;wDACF;oDACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB;GArVwB;;QACM,kIAAA,CAAA,UAAO;QACpB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}