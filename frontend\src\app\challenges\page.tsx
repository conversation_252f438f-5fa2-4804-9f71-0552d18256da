'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { challengesAPI, isDemoMode, mockData } from '@/utils/api';

interface Challenge {
  id: number;
  title: string;
  description: string;
  duration_days: number;
  difficulty_level: 'easy' | 'medium' | 'hard';
  points_reward: number;
  is_active: boolean;
  progress?: number;
  completed_days?: number;
  target_days?: number;
  start_date?: string;
  end_date?: string;
}

interface UserProgress {
  total_points: number;
  completed_challenges: number;
  current_streak: number;
  badges: string[];
}

export default function ChallengesPage() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [activeChallenges, setActiveChallenges] = useState<Challenge[]>([]);
  const [availableChallenges, setAvailableChallenges] = useState<Challenge[]>([]);
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth');
      return;
    }

    const fetchChallengesData = async () => {
      try {
        setIsLoading(true);
        
        if (isDemoMode()) {
          // Use mock data
          const mockActiveChallenges = mockData.challenges.map(challenge => ({
            ...challenge,
            progress: Math.floor(Math.random() * 100),
            completed_days: Math.floor(Math.random() * challenge.duration_days),
            target_days: challenge.duration_days,
            start_date: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
          }));
          
          setActiveChallenges(mockActiveChallenges);
          setAvailableChallenges(generateMockAvailableChallenges());
          setUserProgress(generateMockUserProgress());
        } else {
          // Fetch real data
          const [activeRes, availableRes, progressRes] = await Promise.all([
            challengesAPI.getActive(),
            challengesAPI.getAvailable(),
            challengesAPI.getUserProgress()
          ]);
          
          setActiveChallenges(activeRes);
          setAvailableChallenges(availableRes);
          setUserProgress(progressRes);
        }
      } catch (error) {
        console.error('Error fetching challenges data:', error);
        // Fallback to mock data
        setActiveChallenges(mockData.challenges);
        setAvailableChallenges(generateMockAvailableChallenges());
        setUserProgress(generateMockUserProgress());
      } finally {
        setIsLoading(false);
      }
    };

    fetchChallengesData();
  }, [isAuthenticated, router]);

  const generateMockAvailableChallenges = (): Challenge[] => [
    {
      id: 3,
      title: 'Daily Reflection',
      description: 'Write a daily reflection about your emotions for 14 days',
      duration_days: 14,
      difficulty_level: 'medium',
      points_reward: 200,
      is_active: false,
    },
    {
      id: 4,
      title: 'Stress Management',
      description: 'Practice stress management techniques for 10 days',
      duration_days: 10,
      difficulty_level: 'hard',
      points_reward: 300,
      is_active: false,
    },
    {
      id: 5,
      title: 'Positive Thinking',
      description: 'Focus on positive thoughts and gratitude for 5 days',
      duration_days: 5,
      difficulty_level: 'easy',
      points_reward: 80,
      is_active: false,
    },
  ];

  const generateMockUserProgress = (): UserProgress => ({
    total_points: 425,
    completed_challenges: 3,
    current_streak: 5,
    badges: ['First Steps', 'Week Warrior', 'Mindful Master'],
  });

  const handleJoinChallenge = async (challengeId: number) => {
    try {
      if (isDemoMode()) {
        // Mock joining challenge
        const challenge = availableChallenges.find(c => c.id === challengeId);
        if (challenge) {
          const newActiveChallenge = {
            ...challenge,
            is_active: true,
            progress: 0,
            completed_days: 0,
            target_days: challenge.duration_days,
            start_date: new Date().toISOString(),
          };
          setActiveChallenges([...activeChallenges, newActiveChallenge]);
          setAvailableChallenges(availableChallenges.filter(c => c.id !== challengeId));
        }
      } else {
        await challengesAPI.join(challengeId);
        // Refresh data
        window.location.reload();
      }
    } catch (error) {
      console.error('Error joining challenge:', error);
    }
  };

  const handleCompleteDay = async (challengeId: number) => {
    try {
      if (isDemoMode()) {
        // Mock completing a day
        setActiveChallenges(activeChallenges.map(challenge => {
          if (challenge.id === challengeId) {
            const newCompletedDays = (challenge.completed_days || 0) + 1;
            const newProgress = Math.round((newCompletedDays / challenge.duration_days) * 100);
            return {
              ...challenge,
              completed_days: newCompletedDays,
              progress: newProgress,
            };
          }
          return challenge;
        }));
      } else {
        await challengesAPI.completeDay(challengeId);
        // Refresh data
        window.location.reload();
      }
    } catch (error) {
      console.error('Error completing day:', error);
    }
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Challenges</h1>
              <p className="text-gray-600">Build healthy emotional habits</p>
            </div>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-gray-500 hover:text-gray-700 text-sm font-medium"
            >
              ← Back to Dashboard
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* User Progress */}
          {userProgress && (
            <div className="bg-white shadow rounded-lg mb-6">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Your Progress</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">{userProgress.total_points}</div>
                    <div className="text-sm text-gray-500">Total Points</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">{userProgress.completed_challenges}</div>
                    <div className="text-sm text-gray-500">Completed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600">{userProgress.current_streak}</div>
                    <div className="text-sm text-gray-500">Day Streak</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600">{userProgress.badges.length}</div>
                    <div className="text-sm text-gray-500">Badges Earned</div>
                  </div>
                </div>
                
                {/* Badges */}
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Badges</h4>
                  <div className="flex flex-wrap gap-2">
                    {userProgress.badges.map((badge, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                      >
                        🏆 {badge}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Active Challenges */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Active Challenges</h2>
            {activeChallenges.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {activeChallenges.map((challenge) => (
                  <div key={challenge.id} className="bg-white shadow rounded-lg overflow-hidden">
                    <div className="px-4 py-5 sm:p-6">
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="text-lg font-medium text-gray-900">{challenge.title}</h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getDifficultyColor(challenge.difficulty_level)}`}>
                          {challenge.difficulty_level}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-4">{challenge.description}</p>
                      
                      <div className="mb-4">
                        <div className="flex justify-between text-sm text-gray-600 mb-1">
                          <span>Progress</span>
                          <span>{challenge.completed_days || 0}/{challenge.target_days || challenge.duration_days} days</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${challenge.progress || 0}%` }}
                          ></div>
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-500">🏆 {challenge.points_reward} points</span>
                        <button
                          onClick={() => handleCompleteDay(challenge.id)}
                          disabled={(challenge.completed_days || 0) >= challenge.duration_days}
                          className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm font-medium"
                        >
                          {(challenge.completed_days || 0) >= challenge.duration_days ? 'Completed!' : 'Complete Today'}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-12 text-center">
                  <div className="text-6xl mb-4">🏆</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No active challenges</h3>
                  <p className="text-gray-500">Join a challenge below to start building healthy habits!</p>
                </div>
              </div>
            )}
          </div>

          {/* Available Challenges */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Available Challenges</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {availableChallenges.map((challenge) => (
                <div key={challenge.id} className="bg-white shadow rounded-lg overflow-hidden">
                  <div className="px-4 py-5 sm:p-6">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="text-lg font-medium text-gray-900">{challenge.title}</h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getDifficultyColor(challenge.difficulty_level)}`}>
                        {challenge.difficulty_level}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-4">{challenge.description}</p>
                    
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-sm text-gray-500">📅 {challenge.duration_days} days</span>
                      <span className="text-sm text-gray-500">🏆 {challenge.points_reward} points</span>
                    </div>
                    
                    <button
                      onClick={() => handleJoinChallenge(challenge.id)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      Join Challenge
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
