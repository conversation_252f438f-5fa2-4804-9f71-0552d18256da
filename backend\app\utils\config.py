"""
Configuration settings for the Emotion Detection System
"""

from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    """Application settings"""
    
    # Database - Use SQLite for development if PostgreSQL not available
    database_url: str = "sqlite:///./emotion_detection.db"
    
    # Security
    secret_key: str = "your-secret-key-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Hugging Face
    huggingface_api_key: Optional[str] = None
    emotion_model_name: str = "bsingh/roberta_goEmotion"
    
    # Firebase
    firebase_credentials_path: Optional[str] = None
    
    # API Settings
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    debug: bool = True
    
    # CORS
    allowed_origins: str = "http://localhost:3000,http://127.0.0.1:3000"
    
    # Emotion Categories
    positive_emotions: list = [
        "admiration", "amusement", "approval", "caring", "desire", 
        "excitement", "gratitude", "joy", "love", "optimism", 
        "pride", "relief"
    ]
    
    negative_emotions: list = [
        "anger", "annoyance", "disappointment", "disapproval", "disgust",
        "embarrassment", "fear", "grief", "nervousness", "remorse", "sadness"
    ]
    
    neutral_emotions: list = [
        "confusion", "curiosity", "realization", "surprise", "neutral"
    ]
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Create settings instance
settings = Settings()

# Emotion mapping for primary labels
EMOTION_MAPPING = {
    "positive": settings.positive_emotions,
    "negative": settings.negative_emotions,
    "neutral": settings.neutral_emotions
}

def get_primary_label(emotions_dict: dict) -> str:
    """
    Determine primary emotion label based on emotion probabilities
    
    Args:
        emotions_dict: Dictionary of emotion names and their probabilities
        
    Returns:
        Primary label: "positive", "negative", or "neutral"
    """
    positive_score = sum(
        emotions_dict.get(emotion, 0) 
        for emotion in settings.positive_emotions
    )
    
    negative_score = sum(
        emotions_dict.get(emotion, 0) 
        for emotion in settings.negative_emotions
    )
    
    neutral_score = sum(
        emotions_dict.get(emotion, 0) 
        for emotion in settings.neutral_emotions
    )
    
    # Return the label with highest score
    scores = {
        "positive": positive_score,
        "negative": negative_score,
        "neutral": neutral_score
    }
    
    return max(scores, key=scores.get)
