{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/utils/api.ts"], "sourcesContent": ["/**\n * API utility functions for the Emotion Detection System\n */\n\nimport axios, { AxiosResponse } from 'axios';\n\n// API base URL\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('access_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle auth errors and backend connectivity\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    // Handle authentication errors\n    if (error.response?.status === 401) {\n      // Clear token and redirect to login\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('user');\n      window.location.href = '/auth';\n    }\n\n    // Handle network errors (backend not available)\n    if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n      console.warn('Backend server not available, falling back to demo mode');\n      // Don't redirect, let components handle the fallback\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// Types\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  age?: number;\n  gender?: string;\n  notification_settings: any;\n  created_at: string;\n  is_active: boolean;\n}\n\nexport interface LoginResponse {\n  access_token: string;\n  token_type: string;\n  expires_in: number;\n  user: User;\n}\n\nexport interface EmotionResult {\n  id?: number;\n  text: string;\n  primary_label: string;\n  confidence: number;\n  detected_emotions: Record<string, number>;\n  timestamp: string;\n  user_id: number;\n  input_type?: string;\n}\n\nexport interface EmotionLog {\n  id: number;\n  user_id: number;\n  date: string;\n  text?: string;\n  detected_emotions?: Record<string, number>;\n  primary_label?: string;\n  confidence_score?: number;\n  notes?: string;\n  is_manual: boolean;\n  created_at: string;\n}\n\nexport interface Activity {\n  id: number;\n  emotion_category: string;\n  activity_description: string;\n  link?: string;\n  category?: string;\n  duration_minutes?: number;\n  difficulty_level: string;\n}\n\nexport interface Challenge {\n  id: number;\n  user_id: number;\n  title: string;\n  description: string;\n  start_date: string;\n  end_date: string;\n  status: string;\n  progress: number;\n  target_days: number;\n  completed_days: number;\n  challenge_type: string;\n  reward_points: number;\n  created_at: string;\n  completed_at?: string;\n}\n\nexport interface DailySummary {\n  date: string;\n  primary_emotions: Record<string, number>;\n  emotion_score: number;\n  total_logs: number;\n  dominant_emotion: string;\n}\n\nexport interface MonthlyReport {\n  month: string;\n  year: number;\n  daily_summaries: DailySummary[];\n  emotion_distribution: Record<string, number>;\n  average_score: number;\n  insights: string[];\n  total_logs: number;\n  most_frequent_emotion: string;\n  emotional_stability: string;\n}\n\n// Auth API\nexport const authAPI = {\n  register: async (userData: {\n    name: string;\n    email: string;\n    password: string;\n    age?: number;\n    gender?: string;\n  }): Promise<LoginResponse> => {\n    const response = await api.post('/auth/register', userData);\n    return response.data;\n  },\n\n  login: async (credentials: {\n    email: string;\n    password: string;\n  }): Promise<LoginResponse> => {\n    const response = await api.post('/auth/login', credentials);\n    return response.data;\n  },\n\n  getCurrentUser: async (): Promise<User> => {\n    const response = await api.get('/auth/me');\n    return response.data;\n  },\n\n  updateProfile: async (profileData: {\n    name: string;\n    age?: number;\n    gender?: string;\n  }): Promise<User> => {\n    const response = await api.put('/auth/profile', profileData);\n    return response.data;\n  },\n\n  deleteAccount: async (): Promise<void> => {\n    await api.delete('/auth/account');\n  },\n\n  refreshToken: async (): Promise<LoginResponse> => {\n    const response = await api.post('/auth/refresh');\n    return response.data;\n  },\n};\n\n// Emotion API\nexport const emotionAPI = {\n  detectEmotion: async (text: string): Promise<EmotionResult> => {\n    const response = await api.post('/emotion/detect', { text });\n    return response.data;\n  },\n\n  detectEmotionFromImage: async (imageFile: File): Promise<EmotionResult> => {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await api.post('/emotion/detect-image', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  },\n\n  detectEmotionFromBase64: async (imageData: string): Promise<EmotionResult> => {\n    const response = await api.post('/emotion/detect-base64', { image_data: imageData });\n    return response.data;\n  },\n\n  getHistory: async (limit = 50, offset = 0): Promise<EmotionLog[]> => {\n    const response = await api.get(`/emotion/history?limit=${limit}&offset=${offset}`);\n    return response.data;\n  },\n\n  getRecent: async (): Promise<EmotionLog> => {\n    const response = await api.get('/emotion/recent');\n    return response.data;\n  },\n\n  getStats: async () => {\n    const response = await api.get('/emotion/stats');\n    return response.data;\n  },\n\n  deleteLog: async (logId: number): Promise<void> => {\n    await api.delete(`/emotion/history/${logId}`);\n  },\n};\n\n// Diary API\nexport const diaryAPI = {\n  createLog: async (data: {\n    emotion: string;\n    notes?: string;\n    intensity?: number;\n  }): Promise<EmotionLog> => {\n    const response = await api.post('/diary/log', data);\n    return response.data;\n  },\n\n  getEntries: async (params?: {\n    start_date?: string;\n    end_date?: string;\n    limit?: number;\n    offset?: number;\n  }): Promise<EmotionLog[]> => {\n    const queryParams = new URLSearchParams();\n    if (params?.start_date) queryParams.append('start_date', params.start_date);\n    if (params?.end_date) queryParams.append('end_date', params.end_date);\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\n    if (params?.offset) queryParams.append('offset', params.offset.toString());\n\n    const response = await api.get(`/diary/entries?${queryParams}`);\n    return response.data;\n  },\n\n  getEntry: async (entryId: number): Promise<EmotionLog> => {\n    const response = await api.get(`/diary/entries/${entryId}`);\n    return response.data;\n  },\n\n  updateEntry: async (entryId: number, notes: string): Promise<EmotionLog> => {\n    const response = await api.put(`/diary/entries/${entryId}`, { notes });\n    return response.data;\n  },\n\n  deleteEntry: async (entryId: number): Promise<void> => {\n    await api.delete(`/diary/entries/${entryId}`);\n  },\n\n  getTodayEntries: async (): Promise<EmotionLog[]> => {\n    const response = await api.get('/diary/today');\n    return response.data;\n  },\n\n  getPrompts: async (): Promise<{ prompts: string[] }> => {\n    const response = await api.get('/diary/prompts');\n    return response.data;\n  },\n};\n\n// Activities API\nexport const activitiesAPI = {\n  getSuggestions: async (emotion?: string): Promise<{\n    activities: Activity[];\n    based_on_emotion: string;\n    suggestion_reason: string;\n  }> => {\n    const params = emotion ? `?emotion=${emotion}` : '';\n    const response = await api.get(`/activities/suggestions${params}`);\n    return response.data;\n  },\n\n  getAll: async (): Promise<Activity[]> => {\n    const response = await api.get('/activities/all');\n    return response.data;\n  },\n\n  getByCategory: async (category: string): Promise<Activity[]> => {\n    const response = await api.get(`/activities/by-category/${category}`);\n    return response.data;\n  },\n\n  getPersonalized: async (): Promise<Activity[]> => {\n    const response = await api.get('/activities/personalized');\n    return response.data;\n  },\n};\n\n// Challenges API\nexport const challengesAPI = {\n  create: async (data: {\n    title: string;\n    description: string;\n    target_days: number;\n    challenge_type: string;\n  }): Promise<Challenge> => {\n    const response = await api.post('/challenges/create', data);\n    return response.data;\n  },\n\n  getActive: async (): Promise<Challenge[]> => {\n    const response = await api.get('/challenges/active');\n    return response.data;\n  },\n\n  getAll: async (): Promise<Challenge[]> => {\n    const response = await api.get('/challenges/all');\n    return response.data;\n  },\n\n  update: async (challengeId: number, data: {\n    progress?: number;\n    completed_days?: number;\n    status?: string;\n  }): Promise<Challenge> => {\n    const response = await api.put(`/challenges/${challengeId}`, data);\n    return response.data;\n  },\n\n  delete: async (challengeId: number): Promise<void> => {\n    await api.delete(`/challenges/${challengeId}`);\n  },\n\n  getTemplates: async (): Promise<{ templates: any[] }> => {\n    const response = await api.get('/challenges/templates');\n    return response.data;\n  },\n\n  acceptTemplate: async (templateIndex: number): Promise<Challenge> => {\n    const response = await api.post('/challenges/accept-template', { template_index: templateIndex });\n    return response.data;\n  },\n\n  getBadges: async (): Promise<any[]> => {\n    const response = await api.get('/challenges/badges');\n    return response.data;\n  },\n\n  getAvailable: async (): Promise<Challenge[]> => {\n    const response = await api.get('/challenges/available');\n    return response.data;\n  },\n\n  join: async (challengeId: number): Promise<Challenge> => {\n    const response = await api.post(`/challenges/${challengeId}/join`);\n    return response.data;\n  },\n\n  completeDay: async (challengeId: number): Promise<void> => {\n    await api.post(`/challenges/${challengeId}/complete-day`);\n  },\n\n  getUserProgress: async (): Promise<any> => {\n    const response = await api.get('/challenges/user-progress');\n    return response.data;\n  },\n};\n\n// Reports API\nexport const reportsAPI = {\n  getDailySummary: async (date?: string): Promise<DailySummary> => {\n    const params = date ? `?target_date=${date}` : '';\n    const response = await api.get(`/reports/daily-summary${params}`);\n    return response.data;\n  },\n\n  getWeeklyTrend: async (): Promise<{ weekly_trend: DailySummary[] }> => {\n    const response = await api.get('/reports/weekly-trend');\n    return response.data;\n  },\n\n  getMonthlyReport: async (month?: number, year?: number): Promise<MonthlyReport> => {\n    const params = new URLSearchParams();\n    if (month) params.append('month', month.toString());\n    if (year) params.append('year', year.toString());\n\n    const response = await api.get(`/reports/monthly-report?${params}`);\n    return response.data;\n  },\n};\n\n// Notifications API\nexport const notificationsAPI = {\n  getSettings: async () => {\n    const response = await api.get('/notifications/settings');\n    return response.data;\n  },\n\n  updateSettings: async (settings: any) => {\n    const response = await api.put('/notifications/settings', settings);\n    return response.data;\n  },\n\n  getHistory: async (limit = 50, offset = 0) => {\n    const response = await api.get(`/notifications/history?limit=${limit}&offset=${offset}`);\n    return response.data;\n  },\n\n  markAsRead: async (notificationId: number): Promise<void> => {\n    await api.put(`/notifications/${notificationId}/read`);\n  },\n\n  getUnreadCount: async (): Promise<{ unread_count: number }> => {\n    const response = await api.get('/notifications/unread-count');\n    return response.data;\n  },\n\n  clearAll: async (): Promise<void> => {\n    await api.delete('/notifications/clear-all');\n  },\n};\n\n// Mock data for demo mode when backend isn't available\nexport const mockData = {\n  dailySummary: {\n    emotion_score: 75,\n    dominant_emotion: 'happy',\n    total_logs: 5,\n    date: new Date().toISOString().split('T')[0]\n  },\n\n  activities: [\n    {\n      id: 1,\n      emotion_category: 'positive',\n      activity_description: 'Take a nature walk and appreciate your surroundings',\n      link: null,\n      category: 'outdoor',\n      duration_minutes: 20,\n      difficulty_level: 'easy'\n    },\n    {\n      id: 2,\n      emotion_category: 'positive',\n      activity_description: 'Practice gratitude journaling',\n      link: null,\n      category: 'mindfulness',\n      duration_minutes: 10,\n      difficulty_level: 'easy'\n    },\n    {\n      id: 3,\n      emotion_category: 'general',\n      activity_description: 'Listen to calming music',\n      link: null,\n      category: 'relaxation',\n      duration_minutes: 15,\n      difficulty_level: 'easy'\n    }\n  ],\n\n  challenges: [\n    {\n      id: 1,\n      title: '7-Day Mindfulness Challenge',\n      description: 'Practice mindfulness meditation for 10 minutes daily',\n      duration_days: 7,\n      difficulty_level: 'easy',\n      points_reward: 100,\n      is_active: true\n    },\n    {\n      id: 2,\n      title: 'Gratitude Week',\n      description: 'Write down 3 things you\\'re grateful for each day',\n      duration_days: 7,\n      difficulty_level: 'easy',\n      points_reward: 75,\n      is_active: true\n    }\n  ],\n\n  weeklyTrend: [\n    { date: '2025-07-14', emotion_score: 70, dominant_emotion: 'neutral', total_logs: 3 },\n    { date: '2025-07-15', emotion_score: 65, dominant_emotion: 'sad', total_logs: 4 },\n    { date: '2025-07-16', emotion_score: 80, dominant_emotion: 'happy', total_logs: 6 },\n    { date: '2025-07-17', emotion_score: 75, dominant_emotion: 'happy', total_logs: 5 },\n    { date: '2025-07-18', emotion_score: 85, dominant_emotion: 'joy', total_logs: 7 },\n    { date: '2025-07-19', emotion_score: 72, dominant_emotion: 'content', total_logs: 4 },\n    { date: '2025-07-20', emotion_score: 75, dominant_emotion: 'happy', total_logs: 5 }\n  ]\n};\n\n// Helper function to check if we're in demo mode\nexport const isDemoMode = () => {\n  const token = localStorage.getItem('access_token');\n  return token && token.startsWith('demo-token-');\n};\n\nexport default api;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;AAKoB;AAHrB;;AAEA,eAAe;AACf,MAAM,eAAe,6DAAmC;AAExD,wBAAwB;AACxB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;IAC3C;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,sEAAsE;AACtE,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;QAEK;IADJ,+BAA+B;IAC/B,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;QAClC,oCAAoC;QACpC,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,gDAAgD;IAChD,IAAI,MAAM,IAAI,KAAK,kBAAkB,MAAM,IAAI,KAAK,eAAe;QACjE,QAAQ,IAAI,CAAC;IACb,qDAAqD;IACvD;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AA8FK,MAAM,UAAU;IACrB,UAAU,OAAO;QAOf,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,kBAAkB;QAClD,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,OAAO;QAIZ,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,eAAe;QAC/C,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe,OAAO;QAKpB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iBAAiB;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;QACb,MAAM,IAAI,MAAM,CAAC;IACnB;IAEA,cAAc;QACZ,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;QAChC,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,aAAa;IACxB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;YAAE;QAAK;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,wBAAwB,OAAO;QAC7B,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,yBAAyB,UAAU;YACjE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB,OAAO;QAC9B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,0BAA0B;YAAE,YAAY;QAAU;QAClF,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;YAAO,yEAAQ,IAAI,0EAAS;QACtC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,0BAAyC,OAAhB,OAAM,YAAiB,OAAP;QACzE,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;QACT,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW,OAAO;QAChB,MAAM,IAAI,MAAM,CAAC,AAAC,oBAAyB,OAAN;IACvC;AACF;AAGO,MAAM,WAAW;IACtB,WAAW,OAAO;QAKhB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QAMjB,MAAM,cAAc,IAAI;QACxB,IAAI,mBAAA,6BAAA,OAAQ,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;QAC1E,IAAI,mBAAA,6BAAA,OAAQ,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;QACpE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QAEvE,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,kBAA6B,OAAZ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,kBAAyB,OAAR;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO,SAAiB;QACnC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,kBAAyB,OAAR,UAAW;YAAE;QAAM;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,IAAI,MAAM,CAAC,AAAC,kBAAyB,OAAR;IACrC;IAEA,iBAAiB;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;QACV,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,gBAAgB,OAAO;QAKrB,MAAM,SAAS,UAAU,AAAC,YAAmB,OAAR,WAAY;QACjD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,0BAAgC,OAAP;QACzD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,2BAAmC,OAAT;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,OAAO;QAMb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,sBAAsB;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;QACT,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,aAAqB;QAKlC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,eAA0B,OAAZ,cAAe;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,IAAI,MAAM,CAAC,AAAC,eAA0B,OAAZ;IAClC;IAEA,cAAc;QACZ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,+BAA+B;YAAE,gBAAgB;QAAc;QAC/F,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;QACT,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;QACZ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,OAAO;QACX,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,AAAC,eAA0B,OAAZ,aAAY;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,IAAI,IAAI,CAAC,AAAC,eAA0B,OAAZ,aAAY;IAC5C;IAEA,iBAAiB;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,aAAa;IACxB,iBAAiB,OAAO;QACtB,MAAM,SAAS,OAAO,AAAC,gBAAoB,OAAL,QAAS;QAC/C,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,yBAA+B,OAAP;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB,OAAO,OAAgB;QACvC,MAAM,SAAS,IAAI;QACnB,IAAI,OAAO,OAAO,MAAM,CAAC,SAAS,MAAM,QAAQ;QAChD,IAAI,MAAM,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;QAE7C,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,2BAAiC,OAAP;QAC1D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,mBAAmB;IAC9B,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2BAA2B;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;YAAO,yEAAQ,IAAI,0EAAS;QACtC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,gCAA+C,OAAhB,OAAM,YAAiB,OAAP;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,IAAI,GAAG,CAAC,AAAC,kBAAgC,OAAf,gBAAe;IACjD;IAEA,gBAAgB;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;QACR,MAAM,IAAI,MAAM,CAAC;IACnB;AACF;AAGO,MAAM,WAAW;IACtB,cAAc;QACZ,eAAe;QACf,kBAAkB;QAClB,YAAY;QACZ,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC9C;IAEA,YAAY;QACV;YACE,IAAI;YACJ,kBAAkB;YAClB,sBAAsB;YACtB,MAAM;YACN,UAAU;YACV,kBAAkB;YAClB,kBAAkB;QACpB;QACA;YACE,IAAI;YACJ,kBAAkB;YAClB,sBAAsB;YACtB,MAAM;YACN,UAAU;YACV,kBAAkB;YAClB,kBAAkB;QACpB;QACA;YACE,IAAI;YACJ,kBAAkB;YAClB,sBAAsB;YACtB,MAAM;YACN,UAAU;YACV,kBAAkB;YAClB,kBAAkB;QACpB;KACD;IAED,YAAY;QACV;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,eAAe;YACf,kBAAkB;YAClB,eAAe;YACf,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,eAAe;YACf,kBAAkB;YAClB,eAAe;YACf,WAAW;QACb;KACD;IAED,aAAa;QACX;YAAE,MAAM;YAAc,eAAe;YAAI,kBAAkB;YAAW,YAAY;QAAE;QACpF;YAAE,MAAM;YAAc,eAAe;YAAI,kBAAkB;YAAO,YAAY;QAAE;QAChF;YAAE,MAAM;YAAc,eAAe;YAAI,kBAAkB;YAAS,YAAY;QAAE;QAClF;YAAE,MAAM;YAAc,eAAe;YAAI,kBAAkB;YAAS,YAAY;QAAE;QAClF;YAAE,MAAM;YAAc,eAAe;YAAI,kBAAkB;YAAO,YAAY;QAAE;QAChF;YAAE,MAAM;YAAc,eAAe;YAAI,kBAAkB;YAAW,YAAY;QAAE;QACpF;YAAE,MAAM;YAAc,eAAe;YAAI,kBAAkB;YAAS,YAAY;QAAE;KACnF;AACH;AAGO,MAAM,aAAa;IACxB,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,OAAO,SAAS,MAAM,UAAU,CAAC;AACnC;uCAEe", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { authAPI, User, LoginResponse } from '@/utils/api';\n\ninterface AuthContextType {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (email: string, password: string) => Promise<void>;\n  register: (userData: {\n    name: string;\n    email: string;\n    password: string;\n    age?: number;\n    gender?: string;\n  }) => Promise<void>;\n  logout: () => void;\n  refreshUser: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isHydrated, setIsHydrated] = useState(false);\n\n  const isAuthenticated = !!user;\n\n  // Handle hydration\n  useEffect(() => {\n    setIsHydrated(true);\n  }, []);\n\n  // Check for existing token on mount\n  useEffect(() => {\n    if (!isHydrated) return;\n\n    const initAuth = async () => {\n      try {\n        const token = localStorage.getItem('access_token');\n        const savedUser = localStorage.getItem('user');\n\n        if (token && savedUser) {\n          try {\n            // Verify token is still valid by fetching current user\n            const currentUser = await authAPI.getCurrentUser();\n            setUser(currentUser);\n            localStorage.setItem('user', JSON.stringify(currentUser));\n          } catch (error) {\n            // Token is invalid, clear storage\n            localStorage.removeItem('access_token');\n            localStorage.removeItem('user');\n            setUser(null);\n          }\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    initAuth();\n  }, [isHydrated]);\n\n  const login = async (email: string, password: string): Promise<void> => {\n    try {\n      setIsLoading(true);\n\n      // First, try the actual API call to backend\n      try {\n        const response: LoginResponse = await authAPI.login({ email, password });\n\n        // Store token and user data\n        localStorage.setItem('access_token', response.access_token);\n        localStorage.setItem('user', JSON.stringify(response.user));\n\n        setUser(response.user);\n        return;\n      } catch (apiError: any) {\n        // If backend is not available, check for demo credentials\n        if (email === '<EMAIL>' && password === 'demo123') {\n          console.log('Backend not available, using demo mode');\n          const mockUser = {\n            id: 1,\n            name: 'Demo User',\n            email: '<EMAIL>',\n            age: 25,\n            gender: 'other',\n            notification_settings: {\n              mood_checkins: true,\n              stress_alerts: true,\n              challenge_reminders: true,\n              frequency: 'daily'\n            },\n            created_at: new Date().toISOString(),\n            is_active: true\n          };\n\n          const mockToken = 'demo-token-' + Date.now();\n\n          // Store token and user data\n          localStorage.setItem('access_token', mockToken);\n          localStorage.setItem('user', JSON.stringify(mockUser));\n\n          setUser(mockUser);\n          return;\n        }\n\n        // Re-throw the API error if not demo credentials\n        throw apiError;\n      }\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Login failed';\n\n      // Provide helpful error messages\n      if (error.code === 'ECONNREFUSED' || error.message?.includes('Network Error')) {\n        throw new Error('Cannot connect to server. Please ensure the backend is running on http://localhost:8000');\n      }\n\n      throw new Error(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (userData: {\n    name: string;\n    email: string;\n    password: string;\n    age?: number;\n    gender?: string;\n  }): Promise<void> => {\n    try {\n      setIsLoading(true);\n\n      // For demo purposes, create a mock user with the provided data\n      if (userData.email.includes('@') && userData.password.length >= 6) {\n        const mockUser = {\n          id: Math.floor(Math.random() * 1000) + 1,\n          name: userData.name,\n          email: userData.email,\n          age: userData.age || null,\n          gender: userData.gender || null,\n          notification_settings: {\n            mood_checkins: true,\n            stress_alerts: true,\n            challenge_reminders: true,\n            frequency: 'daily'\n          },\n          created_at: new Date().toISOString(),\n          is_active: true\n        };\n\n        const mockToken = 'demo-token-' + Date.now();\n\n        // Store token and user data\n        localStorage.setItem('access_token', mockToken);\n        localStorage.setItem('user', JSON.stringify(mockUser));\n\n        setUser(mockUser);\n        return;\n      }\n\n      // Try actual API call (will fail if backend isn't running)\n      const response: LoginResponse = await authAPI.register(userData);\n\n      // Store token and user data\n      localStorage.setItem('access_token', response.access_token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n\n      setUser(response.user);\n    } catch (error: any) {\n      throw new Error(error.response?.data?.detail || 'Registration failed. Backend server may not be running.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = (): void => {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n\n  const refreshUser = async (): Promise<void> => {\n    try {\n      const currentUser = await authAPI.getCurrentUser();\n      setUser(currentUser);\n      localStorage.setItem('user', JSON.stringify(currentUser));\n    } catch (error) {\n      console.error('Failed to refresh user:', error);\n      logout();\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    isLoading,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    refreshUser,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,eAA4C;QAAC,EAAE,QAAQ,EAAE;;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB,CAAC,CAAC;IAE1B,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,cAAc;QAChB;iCAAG,EAAE;IAEL,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,YAAY;YAEjB,MAAM;mDAAW;oBACf,IAAI;wBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;wBACnC,MAAM,YAAY,aAAa,OAAO,CAAC;wBAEvC,IAAI,SAAS,WAAW;4BACtB,IAAI;gCACF,uDAAuD;gCACvD,MAAM,cAAc,MAAM,sHAAA,CAAA,UAAO,CAAC,cAAc;gCAChD,QAAQ;gCACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;4BAC9C,EAAE,OAAO,OAAO;gCACd,kCAAkC;gCAClC,aAAa,UAAU,CAAC;gCACxB,aAAa,UAAU,CAAC;gCACxB,QAAQ;4BACV;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;iCAAG;QAAC;KAAW;IAEf,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,aAAa;YAEb,4CAA4C;YAC5C,IAAI;gBACF,MAAM,WAA0B,MAAM,sHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;oBAAE;oBAAO;gBAAS;gBAEtE,4BAA4B;gBAC5B,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;gBAC1D,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;gBAEzD,QAAQ,SAAS,IAAI;gBACrB;YACF,EAAE,OAAO,UAAe;gBACtB,0DAA0D;gBAC1D,IAAI,UAAU,yBAAyB,aAAa,WAAW;oBAC7D,QAAQ,GAAG,CAAC;oBACZ,MAAM,WAAW;wBACf,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,KAAK;wBACL,QAAQ;wBACR,uBAAuB;4BACrB,eAAe;4BACf,eAAe;4BACf,qBAAqB;4BACrB,WAAW;wBACb;wBACA,YAAY,IAAI,OAAO,WAAW;wBAClC,WAAW;oBACb;oBAEA,MAAM,YAAY,gBAAgB,KAAK,GAAG;oBAE1C,4BAA4B;oBAC5B,aAAa,OAAO,CAAC,gBAAgB;oBACrC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;oBAE5C,QAAQ;oBACR;gBACF;gBAEA,iDAAiD;gBACjD,MAAM;YACR;QACF,EAAE,OAAO,OAAY;gBACE,sBAAA,iBAGgB;YAHrC,MAAM,eAAe,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI,MAAM,OAAO,IAAI;YAEtE,iCAAiC;YACjC,IAAI,MAAM,IAAI,KAAK,oBAAkB,iBAAA,MAAM,OAAO,cAAb,qCAAA,eAAe,QAAQ,CAAC,mBAAkB;gBAC7E,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,IAAI,MAAM;QAClB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QAOtB,IAAI;YACF,aAAa;YAEb,+DAA+D;YAC/D,IAAI,SAAS,KAAK,CAAC,QAAQ,CAAC,QAAQ,SAAS,QAAQ,CAAC,MAAM,IAAI,GAAG;gBACjE,MAAM,WAAW;oBACf,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;oBACvC,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,KAAK,SAAS,GAAG,IAAI;oBACrB,QAAQ,SAAS,MAAM,IAAI;oBAC3B,uBAAuB;wBACrB,eAAe;wBACf,eAAe;wBACf,qBAAqB;wBACrB,WAAW;oBACb;oBACA,YAAY,IAAI,OAAO,WAAW;oBAClC,WAAW;gBACb;gBAEA,MAAM,YAAY,gBAAgB,KAAK,GAAG;gBAE1C,4BAA4B;gBAC5B,aAAa,OAAO,CAAC,gBAAgB;gBACrC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAE5C,QAAQ;gBACR;YACF;YAEA,2DAA2D;YAC3D,MAAM,WAA0B,MAAM,sHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;YAEvD,4BAA4B;YAC5B,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;YAC1D,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YAEzD,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAY;gBACH,sBAAA;YAAhB,MAAM,IAAI,MAAM,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;QAClD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,cAAc,MAAM,sHAAA,CAAA,UAAO,CAAC,cAAc;YAChD,QAAQ;YACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC;QACF;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IA9La;KAAA", "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n  setTheme: (theme: Theme) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function ThemeProvider({ children }: { children: React.ReactNode }) {\n  const [theme, setThemeState] = useState<Theme>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n    const initialTheme = savedTheme || systemTheme;\n    \n    setThemeState(initialTheme);\n    setMounted(true);\n  }, []);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (mounted) {\n      const root = document.documentElement;\n      root.classList.remove('light', 'dark');\n      root.classList.add(theme);\n      localStorage.setItem('theme', theme);\n    }\n  }, [theme, mounted]);\n\n  const toggleTheme = () => {\n    setThemeState(prev => prev === 'light' ? 'dark' : 'light');\n  };\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n  };\n\n  // Prevent hydration mismatch\n  if (!mounted) {\n    return <div className=\"animate-pulse bg-gray-100 min-h-screen\" />;\n  }\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>\n      <div className={`min-h-screen transition-colors duration-300 ${\n        theme === 'dark' \n          ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900' \n          : 'bg-gradient-to-br from-blue-50 via-white to-purple-50'\n      }`}>\n        {children}\n      </div>\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\n// Theme-aware component wrapper\nexport function ThemeAware({ children, className = '' }: { children: React.ReactNode; className?: string }) {\n  const { theme } = useTheme();\n  \n  return (\n    <div className={`${className} ${\n      theme === 'dark' \n        ? 'bg-gray-800 text-white border-gray-700' \n        : 'bg-white text-gray-900 border-gray-200'\n    } transition-all duration-300`}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;AAFA;;AAYA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC5B,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;YACzF,MAAM,eAAe,cAAc;YAEnC,cAAc;YACd,WAAW;QACb;kCAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,SAAS;gBACX,MAAM,OAAO,SAAS,eAAe;gBACrC,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;gBAC/B,KAAK,SAAS,CAAC,GAAG,CAAC;gBACnB,aAAa,OAAO,CAAC,SAAS;YAChC;QACF;kCAAG;QAAC;QAAO;KAAQ;IAEnB,MAAM,cAAc;QAClB,cAAc,CAAA,OAAQ,SAAS,UAAU,SAAS;IACpD;IAEA,MAAM,WAAW,CAAC;QAChB,cAAc;IAChB;IAEA,6BAA6B;IAC7B,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC;YAAI,WAAU;;;;;;IACxB;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAa;QAAS;kBAC3D,cAAA,6LAAC;YAAI,WAAW,AAAC,+CAIhB,OAHC,UAAU,SACN,6DACA;sBAEH;;;;;;;;;;;AAIT;GAhDgB;KAAA;AAkDT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS,WAAW,KAA+E;QAA/E,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAqD,GAA/E;;IACzB,MAAM,EAAE,KAAK,EAAE,GAAG;IAElB,qBACE,6LAAC;QAAI,WAAW,AAAC,GACf,OADiB,WAAU,KAI5B,OAHC,UAAU,SACN,2CACA,0CACL;kBACE;;;;;;AAGP;IAZgB;;QACI;;;MADJ", "debugId": null}}]}