{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/app/challenges/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { challengesAPI, isDemoMode, mockData } from '@/utils/api';\n\ninterface Challenge {\n  id: number;\n  title: string;\n  description: string;\n  duration_days: number;\n  difficulty_level: 'easy' | 'medium' | 'hard';\n  points_reward: number;\n  is_active: boolean;\n  progress?: number;\n  completed_days?: number;\n  target_days?: number;\n  start_date?: string;\n  end_date?: string;\n}\n\ninterface UserProgress {\n  total_points: number;\n  completed_challenges: number;\n  current_streak: number;\n  badges: string[];\n}\n\nexport default function ChallengesPage() {\n  const { isAuthenticated } = useAuth();\n  const router = useRouter();\n  const [activeChallenges, setActiveChallenges] = useState<Challenge[]>([]);\n  const [availableChallenges, setAvailableChallenges] = useState<Challenge[]>([]);\n  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/auth');\n      return;\n    }\n\n    const fetchChallengesData = async () => {\n      try {\n        setIsLoading(true);\n        \n        if (isDemoMode()) {\n          // Use mock data\n          const mockActiveChallenges = mockData.challenges.map(challenge => ({\n            ...challenge,\n            progress: Math.floor(Math.random() * 100),\n            completed_days: Math.floor(Math.random() * challenge.duration_days),\n            target_days: challenge.duration_days,\n            start_date: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),\n          }));\n          \n          setActiveChallenges(mockActiveChallenges);\n          setAvailableChallenges(generateMockAvailableChallenges());\n          setUserProgress(generateMockUserProgress());\n        } else {\n          // Fetch real data\n          const [activeRes, availableRes, progressRes] = await Promise.all([\n            challengesAPI.getActive(),\n            challengesAPI.getAvailable(),\n            challengesAPI.getUserProgress()\n          ]);\n          \n          setActiveChallenges(activeRes);\n          setAvailableChallenges(availableRes);\n          setUserProgress(progressRes);\n        }\n      } catch (error) {\n        console.error('Error fetching challenges data:', error);\n        // Fallback to mock data\n        setActiveChallenges(mockData.challenges);\n        setAvailableChallenges(generateMockAvailableChallenges());\n        setUserProgress(generateMockUserProgress());\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchChallengesData();\n  }, [isAuthenticated, router]);\n\n  const generateMockAvailableChallenges = (): Challenge[] => [\n    {\n      id: 3,\n      title: 'Daily Reflection',\n      description: 'Write a daily reflection about your emotions for 14 days',\n      duration_days: 14,\n      difficulty_level: 'medium',\n      points_reward: 200,\n      is_active: false,\n    },\n    {\n      id: 4,\n      title: 'Stress Management',\n      description: 'Practice stress management techniques for 10 days',\n      duration_days: 10,\n      difficulty_level: 'hard',\n      points_reward: 300,\n      is_active: false,\n    },\n    {\n      id: 5,\n      title: 'Positive Thinking',\n      description: 'Focus on positive thoughts and gratitude for 5 days',\n      duration_days: 5,\n      difficulty_level: 'easy',\n      points_reward: 80,\n      is_active: false,\n    },\n  ];\n\n  const generateMockUserProgress = (): UserProgress => ({\n    total_points: 425,\n    completed_challenges: 3,\n    current_streak: 5,\n    badges: ['First Steps', 'Week Warrior', 'Mindful Master'],\n  });\n\n  const handleJoinChallenge = async (challengeId: number) => {\n    try {\n      if (isDemoMode()) {\n        // Mock joining challenge\n        const challenge = availableChallenges.find(c => c.id === challengeId);\n        if (challenge) {\n          const newActiveChallenge = {\n            ...challenge,\n            is_active: true,\n            progress: 0,\n            completed_days: 0,\n            target_days: challenge.duration_days,\n            start_date: new Date().toISOString(),\n          };\n          setActiveChallenges([...activeChallenges, newActiveChallenge]);\n          setAvailableChallenges(availableChallenges.filter(c => c.id !== challengeId));\n        }\n      } else {\n        await challengesAPI.join(challengeId);\n        // Refresh data\n        window.location.reload();\n      }\n    } catch (error) {\n      console.error('Error joining challenge:', error);\n    }\n  };\n\n  const handleCompleteDay = async (challengeId: number) => {\n    try {\n      if (isDemoMode()) {\n        // Mock completing a day\n        setActiveChallenges(activeChallenges.map(challenge => {\n          if (challenge.id === challengeId) {\n            const newCompletedDays = (challenge.completed_days || 0) + 1;\n            const newProgress = Math.round((newCompletedDays / challenge.duration_days) * 100);\n            return {\n              ...challenge,\n              completed_days: newCompletedDays,\n              progress: newProgress,\n            };\n          }\n          return challenge;\n        }));\n      } else {\n        await challengesAPI.completeDay(challengeId);\n        // Refresh data\n        window.location.reload();\n      }\n    } catch (error) {\n      console.error('Error completing day:', error);\n    }\n  };\n\n  const getDifficultyColor = (level: string) => {\n    switch (level) {\n      case 'easy': return 'bg-green-100 text-green-800';\n      case 'medium': return 'bg-yellow-100 text-yellow-800';\n      case 'hard': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Challenges</h1>\n              <p className=\"text-gray-600\">Build healthy emotional habits</p>\n            </div>\n            <button\n              onClick={() => router.push('/dashboard')}\n              className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n            >\n              ← Back to Dashboard\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* User Progress */}\n          {userProgress && (\n            <div className=\"bg-white shadow rounded-lg mb-6\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Your Progress</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-blue-600\">{userProgress.total_points}</div>\n                    <div className=\"text-sm text-gray-500\">Total Points</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-green-600\">{userProgress.completed_challenges}</div>\n                    <div className=\"text-sm text-gray-500\">Completed</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-orange-600\">{userProgress.current_streak}</div>\n                    <div className=\"text-sm text-gray-500\">Day Streak</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-purple-600\">{userProgress.badges.length}</div>\n                    <div className=\"text-sm text-gray-500\">Badges Earned</div>\n                  </div>\n                </div>\n                \n                {/* Badges */}\n                <div className=\"mt-4\">\n                  <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Recent Badges</h4>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {userProgress.badges.map((badge, index) => (\n                      <span\n                        key={index}\n                        className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800\"\n                      >\n                        🏆 {badge}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Active Challenges */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Active Challenges</h2>\n            {activeChallenges.length > 0 ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {activeChallenges.map((challenge) => (\n                  <div key={challenge.id} className=\"bg-white shadow rounded-lg overflow-hidden\">\n                    <div className=\"px-4 py-5 sm:p-6\">\n                      <div className=\"flex justify-between items-start mb-3\">\n                        <h3 className=\"text-lg font-medium text-gray-900\">{challenge.title}</h3>\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getDifficultyColor(challenge.difficulty_level)}`}>\n                          {challenge.difficulty_level}\n                        </span>\n                      </div>\n                      \n                      <p className=\"text-sm text-gray-600 mb-4\">{challenge.description}</p>\n                      \n                      <div className=\"mb-4\">\n                        <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n                          <span>Progress</span>\n                          <span>{challenge.completed_days || 0}/{challenge.target_days || challenge.duration_days} days</span>\n                        </div>\n                        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                          <div\n                            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                            style={{ width: `${challenge.progress || 0}%` }}\n                          ></div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-sm text-gray-500\">🏆 {challenge.points_reward} points</span>\n                        <button\n                          onClick={() => handleCompleteDay(challenge.id)}\n                          disabled={(challenge.completed_days || 0) >= challenge.duration_days}\n                          className=\"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm font-medium\"\n                        >\n                          {(challenge.completed_days || 0) >= challenge.duration_days ? 'Completed!' : 'Complete Today'}\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-12 text-center\">\n                  <div className=\"text-6xl mb-4\">🏆</div>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No active challenges</h3>\n                  <p className=\"text-gray-500\">Join a challenge below to start building healthy habits!</p>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Available Challenges */}\n          <div>\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Available Challenges</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {availableChallenges.map((challenge) => (\n                <div key={challenge.id} className=\"bg-white shadow rounded-lg overflow-hidden\">\n                  <div className=\"px-4 py-5 sm:p-6\">\n                    <div className=\"flex justify-between items-start mb-3\">\n                      <h3 className=\"text-lg font-medium text-gray-900\">{challenge.title}</h3>\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getDifficultyColor(challenge.difficulty_level)}`}>\n                        {challenge.difficulty_level}\n                      </span>\n                    </div>\n                    \n                    <p className=\"text-sm text-gray-600 mb-4\">{challenge.description}</p>\n                    \n                    <div className=\"flex justify-between items-center mb-4\">\n                      <span className=\"text-sm text-gray-500\">📅 {challenge.duration_days} days</span>\n                      <span className=\"text-sm text-gray-500\">🏆 {challenge.points_reward} points</span>\n                    </div>\n                    \n                    <button\n                      onClick={() => handleJoinChallenge(challenge.id)}\n                      className=\"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                    >\n                      Join Challenge\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AA6Be,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACxE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM;gEAAsB;oBAC1B,IAAI;wBACF,aAAa;wBAEb,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;4BAChB,gBAAgB;4BAChB,MAAM,uBAAuB,sHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,GAAG;qGAAC,CAAA,YAAa,CAAC;wCACjE,GAAG,SAAS;wCACZ,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;wCACrC,gBAAgB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,aAAa;wCAClE,aAAa,UAAU,aAAa;wCACpC,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oCACxF,CAAC;;4BAED,oBAAoB;4BACpB,uBAAuB;4BACvB,gBAAgB;wBAClB,OAAO;4BACL,kBAAkB;4BAClB,MAAM,CAAC,WAAW,cAAc,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAC/D,sHAAA,CAAA,gBAAa,CAAC,SAAS;gCACvB,sHAAA,CAAA,gBAAa,CAAC,YAAY;gCAC1B,sHAAA,CAAA,gBAAa,CAAC,eAAe;6BAC9B;4BAED,oBAAoB;4BACpB,uBAAuB;4BACvB,gBAAgB;wBAClB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,wBAAwB;wBACxB,oBAAoB,sHAAA,CAAA,WAAQ,CAAC,UAAU;wBACvC,uBAAuB;wBACvB,gBAAgB;oBAClB,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;mCAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,kCAAkC,IAAmB;YACzD;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,eAAe;gBACf,kBAAkB;gBAClB,eAAe;gBACf,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,eAAe;gBACf,kBAAkB;gBAClB,eAAe;gBACf,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,eAAe;gBACf,kBAAkB;gBAClB,eAAe;gBACf,WAAW;YACb;SACD;IAED,MAAM,2BAA2B,IAAoB,CAAC;YACpD,cAAc;YACd,sBAAsB;YACtB,gBAAgB;YAChB,QAAQ;gBAAC;gBAAe;gBAAgB;aAAiB;QAC3D,CAAC;IAED,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;gBAChB,yBAAyB;gBACzB,MAAM,YAAY,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACzD,IAAI,WAAW;oBACb,MAAM,qBAAqB;wBACzB,GAAG,SAAS;wBACZ,WAAW;wBACX,UAAU;wBACV,gBAAgB;wBAChB,aAAa,UAAU,aAAa;wBACpC,YAAY,IAAI,OAAO,WAAW;oBACpC;oBACA,oBAAoB;2BAAI;wBAAkB;qBAAmB;oBAC7D,uBAAuB,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAClE;YACF,OAAO;gBACL,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;gBACzB,eAAe;gBACf,OAAO,QAAQ,CAAC,MAAM;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;gBAChB,wBAAwB;gBACxB,oBAAoB,iBAAiB,GAAG,CAAC,CAAA;oBACvC,IAAI,UAAU,EAAE,KAAK,aAAa;wBAChC,MAAM,mBAAmB,CAAC,UAAU,cAAc,IAAI,CAAC,IAAI;wBAC3D,MAAM,cAAc,KAAK,KAAK,CAAC,AAAC,mBAAmB,UAAU,aAAa,GAAI;wBAC9E,OAAO;4BACL,GAAG,SAAS;4BACZ,gBAAgB;4BAChB,UAAU;wBACZ;oBACF;oBACA,OAAO;gBACT;YACF,OAAO;gBACL,MAAM,sHAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;gBAChC,eAAe;gBACf,OAAO,QAAQ,CAAC,MAAM;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,8BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDACjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC,aAAa,YAAY;;;;;;kEAC5E,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAqC,aAAa,oBAAoB;;;;;;kEACrF,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC,aAAa,cAAc;;;;;;kEAChF,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC,aAAa,MAAM,CAAC,MAAM;;;;;;kEAC/E,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAK3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;0DACZ,aAAa,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC/B,6LAAC;wDAEC,WAAU;;4DACX;4DACK;;uDAHC;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAanB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;gCACxD,iBAAiB,MAAM,GAAG,kBACzB,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,0BACrB,6LAAC;4CAAuB,WAAU;sDAChC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAqC,UAAU,KAAK;;;;;;0EAClE,6LAAC;gEAAK,WAAW,AAAC,2EAAyH,OAA/C,mBAAmB,UAAU,gBAAgB;0EACtI,UAAU,gBAAgB;;;;;;;;;;;;kEAI/B,6LAAC;wDAAE,WAAU;kEAA8B,UAAU,WAAW;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;;4EAAM,UAAU,cAAc,IAAI;4EAAE;4EAAE,UAAU,WAAW,IAAI,UAAU,aAAa;4EAAC;;;;;;;;;;;;;0EAE1F,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,AAAC,GAA0B,OAAxB,UAAU,QAAQ,IAAI,GAAE;oEAAG;;;;;;;;;;;;;;;;;kEAKpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAwB;oEAAI,UAAU,aAAa;oEAAC;;;;;;;0EACpE,6LAAC;gEACC,SAAS,IAAM,kBAAkB,UAAU,EAAE;gEAC7C,UAAU,CAAC,UAAU,cAAc,IAAI,CAAC,KAAK,UAAU,aAAa;gEACpE,WAAU;0EAET,CAAC,UAAU,cAAc,IAAI,CAAC,KAAK,UAAU,aAAa,GAAG,eAAe;;;;;;;;;;;;;;;;;;2CA/B3E,UAAU,EAAE;;;;;;;;;yDAuC1B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCAOrC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;8CACZ,oBAAoB,GAAG,CAAC,CAAC,0BACxB,6LAAC;4CAAuB,WAAU;sDAChC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAqC,UAAU,KAAK;;;;;;0EAClE,6LAAC;gEAAK,WAAW,AAAC,2EAAyH,OAA/C,mBAAmB,UAAU,gBAAgB;0EACtI,UAAU,gBAAgB;;;;;;;;;;;;kEAI/B,6LAAC;wDAAE,WAAU;kEAA8B,UAAU,WAAW;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAwB;oEAAI,UAAU,aAAa;oEAAC;;;;;;;0EACpE,6LAAC;gEAAK,WAAU;;oEAAwB;oEAAI,UAAU,aAAa;oEAAC;;;;;;;;;;;;;kEAGtE,6LAAC;wDACC,SAAS,IAAM,oBAAoB,UAAU,EAAE;wDAC/C,WAAU;kEACX;;;;;;;;;;;;2CAnBK,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BtC;GAjUwB;;QACM,kIAAA,CAAA,UAAO;QACpB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}