"""
Test database connection and user creation
"""

import asyncio
from app.models.database import database, users_table
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def test_db_operations():
    """Test database operations"""
    try:
        print("🔧 Testing database connection...")
        
        # Connect to database
        await database.connect()
        print("✅ Database connected")
        
        # Test simple query
        result = await database.fetch_one("SELECT 1 as test")
        print(f"✅ Simple query successful: {result}")
        
        # Test user table exists
        try:
            count = await database.fetch_one("SELECT COUNT(*) as count FROM users")
            print(f"✅ Users table exists with {count['count']} users")
        except Exception as e:
            print(f"❌ Users table issue: {e}")
            await database.disconnect()
            return False
        
        # Test user creation
        try:
            hashed_password = pwd_context.hash("testpassword")
            
            # Check if test user exists
            existing = await database.fetch_one(
                users_table.select().where(users_table.c.email == "<EMAIL>")
            )
            
            if existing:
                print("✅ Test user already exists")
            else:
                # Create test user
                query = users_table.insert().values(
                    name="DB Test User",
                    email="<EMAIL>",
                    password_hash=hashed_password,
                    age=30,
                    gender="other",
                    notification_settings={
                        "mood_checkins": True,
                        "stress_alerts": True,
                        "challenge_reminders": True,
                        "frequency": "daily"
                    }
                )
                
                user_id = await database.execute(query)
                print(f"✅ Test user created with ID: {user_id}")
                
                # Fetch created user
                user = await database.fetch_one(
                    users_table.select().where(users_table.c.id == user_id)
                )
                print(f"✅ User fetched: {user['name']} ({user['email']})")
        
        except Exception as e:
            print(f"❌ User creation failed: {e}")
            await database.disconnect()
            return False
        
        await database.disconnect()
        print("✅ Database operations successful")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_db_operations())
    if success:
        print("🎉 Database test complete!")
    else:
        print("❌ Database test failed!")
