# Emotion Detection System Startup Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Emotion Detection System Startup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set project directory
$projectDir = "f:\final year project\whole project"
Set-Location $projectDir

Write-Host "🔧 Project Directory: $projectDir" -ForegroundColor Yellow
Write-Host ""

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Check if backend is already running
if (Test-Port 8000) {
    Write-Host "⚠️  Backend server already running on port 8000" -ForegroundColor Yellow
} else {
    Write-Host "🚀 Starting Backend Server..." -ForegroundColor Green
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    # Start backend in new window
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$projectDir\backend'; Write-Host 'Installing Python dependencies...' -ForegroundColor Yellow; pip install -r requirements.txt; Write-Host 'Starting backend server...' -ForegroundColor Green; python start.py"
    
    Write-Host "✅ Backend server starting in new window..." -ForegroundColor Green
}

Write-Host ""
Write-Host "⏳ Waiting for backend to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# Check if frontend is already running
if (Test-Port 3000) {
    Write-Host "⚠️  Frontend server already running on port 3000" -ForegroundColor Yellow
} else {
    Write-Host "🌐 Starting Frontend Server..." -ForegroundColor Green
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    # Start frontend in new window
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$projectDir\frontend'; Write-Host 'Installing Node dependencies...' -ForegroundColor Yellow; npm install; Write-Host 'Starting frontend server...' -ForegroundColor Green; npm run dev"
    
    Write-Host "✅ Frontend server starting in new window..." -ForegroundColor Green
}

Write-Host ""
Write-Host "🎯 Startup Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Access Points:" -ForegroundColor Cyan
Write-Host "   Frontend: http://localhost:3000" -ForegroundColor White
Write-Host "   Backend API: http://localhost:8000/docs" -ForegroundColor White
Write-Host "   Health Check: http://localhost:8000/health" -ForegroundColor White
Write-Host ""
Write-Host "🔑 Demo Login Credentials:" -ForegroundColor Cyan
Write-Host "   Email: <EMAIL>" -ForegroundColor White
Write-Host "   Password: demo123" -ForegroundColor White
Write-Host ""
Write-Host "💡 Integration Features:" -ForegroundColor Cyan
Write-Host "   ✅ Automatic fallback to demo mode if backend unavailable" -ForegroundColor Green
Write-Host "   ✅ Real-time API integration when backend is running" -ForegroundColor Green
Write-Host "   ✅ CORS properly configured for cross-origin requests" -ForegroundColor Green
Write-Host "   ✅ SQLite database for easy development setup" -ForegroundColor Green
Write-Host ""
Write-Host "⏰ Please wait 30-60 seconds for both servers to fully start" -ForegroundColor Yellow
Write-Host ""

# Wait for user input
Read-Host "Press Enter to open the application in browser"

# Open browser
Start-Process "http://localhost:3000"

Write-Host "🌐 Application opened in browser!" -ForegroundColor Green
