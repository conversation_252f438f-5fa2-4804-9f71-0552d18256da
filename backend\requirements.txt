# Core FastAPI dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# Database dependencies
sqlalchemy>=2.0.0
databases[postgresql]>=0.8.0
asyncpg>=0.29.0

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Machine Learning and NLP (lighter versions)
transformers>=4.30.0
torch>=2.0.0
huggingface-hub>=0.19.0

# Image processing dependencies
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0

# Environment and configuration
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# HTTP client for external APIs
httpx>=0.25.0

# Data processing utilities
python-dateutil>=2.8.0
