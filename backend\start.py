"""
Startup script for the Emotion Detection System backend
"""

import uvicorn
import asyncio
from app.main import app
from app.models.database import database, metadata, engine
from app.utils.config import settings

async def create_tables():
    """Create database tables if they don't exist"""
    try:
        # Create all tables
        metadata.create_all(bind=engine)
        print("✅ Database tables created successfully")
        
        # Test database connection
        await database.connect()
        await database.fetch_one("SELECT 1")
        await database.disconnect()
        print("✅ Database connection test successful")
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        print("Please make sure PostgreSQL is running and the database exists")
        return False
    
    return True

async def seed_default_data():
    """Seed the database with default activities"""
    try:
        await database.connect()
        
        # Import here to avoid circular imports
        from app.models.database import activities_table
        
        # Check if activities already exist
        existing = await database.fetch_one(activities_table.select().limit(1))
        
        if not existing:
            print("🌱 Seeding default activities...")
            
            default_activities = [
                # Positive activities
                {
                    "emotion_category": "positive",
                    "activity_description": "Share your joy with friends or family",
                    "link": None,
                    "category": "social",
                    "duration_minutes": 30,
                    "difficulty_level": "easy"
                },
                {
                    "emotion_category": "positive",
                    "activity_description": "Take a nature walk and appreciate your surroundings",
                    "link": None,
                    "category": "outdoor",
                    "duration_minutes": 20,
                    "difficulty_level": "easy"
                },
                {
                    "emotion_category": "positive",
                    "activity_description": "Practice gratitude journaling",
                    "link": None,
                    "category": "mindfulness",
                    "duration_minutes": 10,
                    "difficulty_level": "easy"
                },
                
                # Negative emotion activities
                {
                    "emotion_category": "negative",
                    "activity_description": "Try a 5-minute breathing exercise",
                    "link": "https://www.youtube.com/watch?v=YRPh_GaiL8s",
                    "category": "mindfulness",
                    "duration_minutes": 5,
                    "difficulty_level": "easy"
                },
                {
                    "emotion_category": "negative",
                    "activity_description": "Listen to calming music",
                    "link": None,
                    "category": "relaxation",
                    "duration_minutes": 15,
                    "difficulty_level": "easy"
                },
                {
                    "emotion_category": "negative",
                    "activity_description": "Do some light stretching or yoga",
                    "link": "https://www.youtube.com/watch?v=v7AYKMP6rOE",
                    "category": "exercise",
                    "duration_minutes": 15,
                    "difficulty_level": "easy"
                },
                
                # Neutral activities
                {
                    "emotion_category": "neutral",
                    "activity_description": "Read a book or article",
                    "link": None,
                    "category": "learning",
                    "duration_minutes": 30,
                    "difficulty_level": "easy"
                },
                {
                    "emotion_category": "neutral",
                    "activity_description": "Try a new hobby or skill",
                    "link": None,
                    "category": "creative",
                    "duration_minutes": 45,
                    "difficulty_level": "medium"
                },
                
                # General activities
                {
                    "emotion_category": "general",
                    "activity_description": "Practice mindfulness meditation",
                    "link": "https://www.headspace.com",
                    "category": "mindfulness",
                    "duration_minutes": 10,
                    "difficulty_level": "easy"
                },
                {
                    "emotion_category": "general",
                    "activity_description": "Go for a walk outside",
                    "link": None,
                    "category": "exercise",
                    "duration_minutes": 20,
                    "difficulty_level": "easy"
                }
            ]
            
            for activity in default_activities:
                query = activities_table.insert().values(**activity)
                await database.execute(query)
            
            print(f"✅ Seeded {len(default_activities)} default activities")
        else:
            print("✅ Default activities already exist")
        
        await database.disconnect()
        
    except Exception as e:
        print(f"❌ Failed to seed default data: {e}")

def main():
    """Main startup function"""
    print("🚀 Starting Emotion Detection System Backend...")
    print(f"📊 Database URL: {settings.database_url}")
    print(f"🌐 API Host: {settings.api_host}:{settings.api_port}")
    print(f"🔧 Debug Mode: {settings.debug}")
    
    # Setup database
    setup_success = asyncio.run(create_tables())
    if not setup_success:
        return
    
    # Seed default data
    asyncio.run(seed_default_data())
    
    print("\n🎯 Backend setup complete!")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("\n🚀 Starting server...")
    
    # Start the server
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level="info" if settings.debug else "warning"
    )

if __name__ == "__main__":
    main()
