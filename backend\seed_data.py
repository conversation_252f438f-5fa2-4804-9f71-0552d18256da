"""
Seed the database with default activities
"""

import asyncio
from app.models.database import database, activities_table

async def seed_activities():
    """Seed default activities"""
    try:
        await database.connect()
        
        # Check if activities already exist
        existing = await database.fetch_one(activities_table.select().limit(1))
        
        if existing:
            print("✅ Activities already exist in database")
            await database.disconnect()
            return True
        
        print("🌱 Seeding default activities...")
        
        default_activities = [
            # Positive activities
            {
                "emotion_category": "positive",
                "activity_description": "Share your joy with friends or family",
                "link": None,
                "category": "social",
                "duration_minutes": 30,
                "difficulty_level": "easy"
            },
            {
                "emotion_category": "positive",
                "activity_description": "Take a nature walk and appreciate your surroundings",
                "link": None,
                "category": "outdoor",
                "duration_minutes": 20,
                "difficulty_level": "easy"
            },
            {
                "emotion_category": "positive",
                "activity_description": "Practice gratitude journaling",
                "link": None,
                "category": "mindfulness",
                "duration_minutes": 10,
                "difficulty_level": "easy"
            },
            
            # Negative emotion activities
            {
                "emotion_category": "negative",
                "activity_description": "Try a 5-minute breathing exercise",
                "link": "https://www.youtube.com/watch?v=YRPh_GaiL8s",
                "category": "mindfulness",
                "duration_minutes": 5,
                "difficulty_level": "easy"
            },
            {
                "emotion_category": "negative",
                "activity_description": "Listen to calming music",
                "link": None,
                "category": "relaxation",
                "duration_minutes": 15,
                "difficulty_level": "easy"
            },
            {
                "emotion_category": "negative",
                "activity_description": "Do some light stretching or yoga",
                "link": "https://www.youtube.com/watch?v=v7AYKMP6rOE",
                "category": "exercise",
                "duration_minutes": 15,
                "difficulty_level": "easy"
            },
            {
                "emotion_category": "negative",
                "activity_description": "Call a friend or family member",
                "link": None,
                "category": "social",
                "duration_minutes": 20,
                "difficulty_level": "easy"
            },
            
            # Neutral activities
            {
                "emotion_category": "neutral",
                "activity_description": "Read a book or article",
                "link": None,
                "category": "learning",
                "duration_minutes": 30,
                "difficulty_level": "easy"
            },
            {
                "emotion_category": "neutral",
                "activity_description": "Try a new hobby or skill",
                "link": None,
                "category": "creative",
                "duration_minutes": 45,
                "difficulty_level": "medium"
            },
            {
                "emotion_category": "neutral",
                "activity_description": "Organize your living space",
                "link": None,
                "category": "productive",
                "duration_minutes": 30,
                "difficulty_level": "easy"
            },
            
            # General activities
            {
                "emotion_category": "general",
                "activity_description": "Practice mindfulness meditation",
                "link": "https://www.headspace.com",
                "category": "mindfulness",
                "duration_minutes": 10,
                "difficulty_level": "easy"
            },
            {
                "emotion_category": "general",
                "activity_description": "Go for a walk outside",
                "link": None,
                "category": "exercise",
                "duration_minutes": 20,
                "difficulty_level": "easy"
            }
        ]
        
        # Insert activities
        for activity in default_activities:
            query = activities_table.insert().values(**activity)
            await database.execute(query)
        
        print(f"✅ Seeded {len(default_activities)} default activities")
        await database.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Failed to seed activities: {e}")
        await database.disconnect()
        return False

if __name__ == "__main__":
    success = asyncio.run(seed_activities())
    if success:
        print("🎉 Activity seeding complete!")
    else:
        print("❌ Activity seeding failed!")
