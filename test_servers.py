"""
Simple script to test if both servers are running
"""

import requests
import time

def test_backend():
    """Test backend server"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running on http://localhost:8000")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Backend returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend is not responding: {e}")
        return False

def test_frontend():
    """Test frontend server"""
    try:
        # Try both possible ports
        for port in [3000, 3001]:
            try:
                response = requests.get(f"http://localhost:{port}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ Frontend is running on http://localhost:{port}")
                    return True
            except requests.exceptions.RequestException:
                continue
        
        print("❌ Frontend is not responding on ports 3000 or 3001")
        return False
    except Exception as e:
        print(f"❌ Error testing frontend: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Emotion Detection System Servers")
    print("=" * 50)
    
    backend_ok = test_backend()
    frontend_ok = test_frontend()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Backend:  {'✅ Running' if backend_ok else '❌ Not running'}")
    print(f"Frontend: {'✅ Running' if frontend_ok else '❌ Not running'}")
    
    if backend_ok and frontend_ok:
        print("\n🎉 Both servers are running successfully!")
        print("🌐 You can now access the application:")
        print("   - Frontend: http://localhost:3000 or http://localhost:3001")
        print("   - Backend API: http://localhost:8000/docs")
    elif backend_ok:
        print("\n⚠️  Backend is running but frontend needs to be started")
        print("   Run: cd frontend && npm run dev")
    elif frontend_ok:
        print("\n⚠️  Frontend is running but backend needs to be started")
        print("   Run: cd backend && python -m uvicorn app.main:app --reload --port 8000")
    else:
        print("\n❌ Both servers need to be started")
        print("   Backend: cd backend && python -m uvicorn app.main:app --reload --port 8000")
        print("   Frontend: cd frontend && npm run dev")

if __name__ == "__main__":
    main()
