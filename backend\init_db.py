"""
Initialize the database with tables
"""

import asyncio
from app.models.database import database, metadata, engine

async def init_database():
    """Initialize database tables"""
    try:
        print("🔧 Creating database tables...")
        
        # Create all tables
        metadata.create_all(bind=engine)
        print("✅ Database tables created successfully")
        
        # Test database connection
        await database.connect()
        result = await database.fetch_one("SELECT 1 as test")
        print(f"✅ Database connection test successful: {result}")
        await database.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(init_database())
    if success:
        print("🎉 Database initialization complete!")
    else:
        print("❌ Database initialization failed!")
