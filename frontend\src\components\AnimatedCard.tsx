'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { ReactNode, useState } from 'react';

interface AnimatedCardProps {
  children: ReactNode;
  className?: string;
  hover?: boolean;
  gradient?: boolean;
  glow?: boolean;
  delay?: number;
}

export default function AnimatedCard({ 
  children, 
  className = '', 
  hover = true, 
  gradient = false,
  glow = false,
  delay = 0
}: AnimatedCardProps) {
  const { theme } = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  const baseClasses = `
    transform transition-all duration-500 ease-out
    ${delay > 0 ? `animate-fade-in-up` : ''}
    ${hover ? 'hover:scale-105 hover:-translate-y-2' : ''}
    ${glow && isHovered ? 'shadow-2xl' : 'shadow-lg'}
  `;

  const themeClasses = theme === 'dark'
    ? `bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 text-white
       ${glow && isHovered ? 'shadow-purple-500/25' : ''}
       ${gradient ? 'bg-gradient-to-br from-purple-900/50 to-blue-900/50' : ''}`
    : `bg-white border border-gray-200 text-gray-900
       ${glow && isHovered ? 'shadow-blue-500/25' : ''}
       ${gradient ? 'bg-gradient-to-br from-blue-50 to-purple-50' : ''}`;

  return (
    <div
      className={`${baseClasses} ${themeClasses} ${className} rounded-xl overflow-hidden`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ animationDelay: `${delay}ms` }}
    >
      {/* Animated background overlay */}
      {gradient && (
        <div className={`absolute inset-0 opacity-0 transition-opacity duration-500 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        } ${
          theme === 'dark'
            ? 'bg-gradient-to-br from-purple-600/20 to-blue-600/20'
            : 'bg-gradient-to-br from-blue-400/10 to-purple-400/10'
        }`} />
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Glow effect */}
      {glow && (
        <div className={`absolute inset-0 rounded-xl transition-opacity duration-500 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        } ${
          theme === 'dark'
            ? 'bg-gradient-to-r from-purple-600/10 to-blue-600/10'
            : 'bg-gradient-to-r from-blue-400/10 to-purple-400/10'
        } blur-xl -z-10`} />
      )}
    </div>
  );
}

// Fade in animation component
export function FadeInUp({ children, delay = 0, className = '' }: {
  children: ReactNode;
  delay?: number;
  className?: string;
}) {
  return (
    <div 
      className={`animate-fade-in-up ${className}`}
      style={{ animationDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );
}

// Pulse animation component
export function PulseGlow({ children, color = 'blue' }: {
  children: ReactNode;
  color?: 'blue' | 'purple' | 'green' | 'red' | 'yellow';
}) {
  const { theme } = useTheme();
  
  const colorClasses = {
    blue: theme === 'dark' ? 'shadow-blue-500/50' : 'shadow-blue-400/50',
    purple: theme === 'dark' ? 'shadow-purple-500/50' : 'shadow-purple-400/50',
    green: theme === 'dark' ? 'shadow-green-500/50' : 'shadow-green-400/50',
    red: theme === 'dark' ? 'shadow-red-500/50' : 'shadow-red-400/50',
    yellow: theme === 'dark' ? 'shadow-yellow-500/50' : 'shadow-yellow-400/50',
  };

  return (
    <div className={`animate-pulse-glow ${colorClasses[color]}`}>
      {children}
    </div>
  );
}
